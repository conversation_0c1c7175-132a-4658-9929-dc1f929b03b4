import re
import copy
from typing import Dict, Any


def populate_config(config: Dict[str, Any], **kwargs) -> Dict[str, Any]:
    """
    Recursively populate a configuration dictionary with values from kwargs.

    This function will:
    1. Replace {key} values with the corresponding value from kwargs
    2. Handle dependent keys by making multiple passes
    3. Add special 'yaml_path_from_stacks' variable to identify nested paths

    Args:
        config: The configuration dictionary to populate
        **kwargs: Key-value pairs to use for substitution

    Returns:
        A new dict with all placeholders replaced with actual values
    """
    # Create a copy to avoid modifying the original
    result = copy.deepcopy(config)

    # Identify bracketed values using regex
    pattern = re.compile(r"\{([^{}]+)\}")

    # Keep track of all values that have been processed
    context = {key.replace("_", "-"): value for key, value in kwargs.items()}

    # Add config values that might be needed for substitution
    for key, value in result.items():
        if isinstance(value, str) and not pattern.search(value):
            context[key] = value

    # Function to check if a string has any remaining placeholders
    def has_placeholders(s):
        return isinstance(s, str) and pattern.search(s)

    # Function to check if the entire config has any remaining placeholders
    def config_has_placeholders(cfg):
        if isinstance(cfg, dict):
            for k, v in cfg.items():
                if has_placeholders(k) or config_has_placeholders(v):
                    return True
        elif isinstance(cfg, list):
            for item in cfg:
                if config_has_placeholders(item):
                    return True
        elif has_placeholders(cfg):
            return True
        return False

    # Function to process a single value
    def process_value(value, path=""):
        if isinstance(value, dict):
            # Process dictionary
            new_dict = {}
            for k, v in value.items():
                # Add the current path to the context for this branch
                current_path = f"{path}.{k}" if path else k
                new_dict[k] = process_value(v, current_path)
            return new_dict

        elif isinstance(value, list):
            # Process list
            return [process_value(item, path) for item in value]

        elif isinstance(value, str) and pattern.search(value):
            # Process string with placeholders
            matches = pattern.findall(value)

            # Substitute each placeholder if possible
            new_value = value
            for match in matches:
                # Special handling for yaml_path_from_stacks
                if match == "yaml_path_from_stacks" and path:
                    # Extract path starting from 'stacks'
                    stack_path = ""
                    parts = path.split(".")
                    if "stacks" in parts:
                        idx = parts.index("stacks")
                        stack_path = "-".join(parts[idx + 1 :])

                    placeholder = f"{{{match}}}"
                    if placeholder in new_value:
                        new_value = new_value.replace(placeholder, stack_path)

                # Regular substitution
                elif match in context:
                    placeholder = f"{{{match}}}"
                    if placeholder in new_value:
                        new_value = new_value.replace(placeholder, context[match])

            return new_value
        else:
            # Return unchanged value
            return value

    # Process until no more changes or max iterations reached
    max_iterations = 10
    iterations = 0

    while config_has_placeholders(result) and iterations < max_iterations:
        iterations += 1

        # Process the entire config
        result = process_value(result)

        # Update context with new values
        def update_context(cfg, path=""):
            if isinstance(cfg, dict):
                for k, v in cfg.items():
                    current_path = f"{path}.{k}" if path else k
                    if isinstance(v, str) and not has_placeholders(v):
                        context[k] = v
                        # Also add with full path for more specific references
                        context[current_path] = v
                    update_context(v, current_path)
            elif isinstance(cfg, list):
                for i, item in enumerate(cfg):
                    update_context(item, f"{path}[{i}]")

        update_context(result)

    return result
