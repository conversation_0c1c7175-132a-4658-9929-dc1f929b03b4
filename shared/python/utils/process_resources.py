import sys

import boto3


def get_param_values_from_stack_config(config, resource_id):
    """
    Extracts parameter value from the stack configuration for a specific resource ID.

    Parameters:
    - config: Dictionary containing stack configuration
    - resource_id: Single resource ID to get parameter value for

    Returns:
    - Parameter value if resource exists and has create-parameter-store-variable-with-arn set to true
    - None if resource not found or doesn't have the required property
    """
    # Get resources with parameter store variables
    param_resources_config = get_parameter_store_resources(config)

    # Find the specific resource that matches the provided ID
    matching_resource = None
    for resource in param_resources_config:
        if resource.get("id") == resource_id:
            matching_resource = resource
            break

    if not matching_resource:
        print(
            f"Resource with ID '{resource_id}' not found or doesn't have create-parameter-store-variable-with-arn set to true"
        )
        return None

    # Initialize SSM client and get parameter value
    ssm_client = boto3.client("ssm")
    param_value = get_param_value(ssm_client, resource_id)

    return param_value


def get_parameter_store_resources(config_stack):
    """
    Recursively extracts IDs from resources that have create-parameter-store-variable-with-arn set to true

    Parameters:
    - config_stack: Dictionary containing configuration for AWS resources

    Returns:
    - List of dictionaries containing resource 'id' and 'path' where create-parameter-store-variable-with-arn is true
    """
    resources = []

    def traverse_dict(d, path=""):
        if isinstance(d, dict):
            # Check if this dictionary has both 'id' and 'create-parameter-store-variable-with-arn' = True
            if "id" in d and d.get("create-parameter-store-variable-with-arn") is True:
                resources.append({"id": d["id"], "path": path})

            # Continue recursively traversing the dictionary
            for key, value in d.items():
                new_path = f"{path}.{key}" if path else key
                traverse_dict(value, new_path)
        elif isinstance(d, list):
            # Traverse each item in the list
            for i, item in enumerate(d):
                new_path = f"{path}[{i}]"
                traverse_dict(item, new_path)

    traverse_dict(config_stack)
    return resources


def find_matching_items_in_list(list, key):
    return next(
        (item for item in list if key in item),
        None,
    )


def get_param_value(ssm_client, param_name):
    # Get the parameter value from Parameter Store
    try:
        param = ssm_client.get_parameter(Name=param_name, WithDecryption=True)
        return param["Parameter"]["Value"]
    except Exception as e:
        print(f"Error retrieving parameter {param_name} from Parameter Store: {e}")
        sys.exit(1)


def get_subnets_by_name_keyword(vpc_id, keyword, aws_region=None):
    """
    Returns subnet IDs for subnets in the specified VPC that have the keyword in their Name tag

    Parameters:
    - vpc_id: ID of the VPC to search in
    - keyword: Keyword to search for in subnet names
    - aws_region: AWS region to connect to (optional)

    Returns:
    - List of matching subnet IDs
    """
    # Initialize EC2 client
    if aws_region:
        ec2_client = boto3.client("ec2", region_name=aws_region)
    else:
        ec2_client = boto3.client("ec2")

    # Get all subnets in the specified VPC
    response = ec2_client.describe_subnets(
        Filters=[{"Name": "vpc-id", "Values": [vpc_id]}]
    )

    matching_subnet_ids = []

    # Filter subnets by Name tag containing the keyword
    for subnet in response["Subnets"]:
        subnet_id = subnet["SubnetId"]
        # Check if the subnet has tags
        if "Tags" in subnet:
            # Look for the Name tag
            for tag in subnet["Tags"]:
                if tag["Key"] == "Name" and keyword.lower() in tag["Value"].lower():
                    matching_subnet_ids.append(subnet_id)
                    break

    return matching_subnet_ids


def get_process_resource_ids(aws_config, process_name):
    """
    Returns all infrastructure resources that have the specified process_name as a consumer.

    Args:
        aws_config (dict): The AWS configuration dictionary
        process_name (str): The name of the process to filter resources by

    Returns:
        dict: A dictionary containing all resources (buckets, secrets, etc.) that have
              the specified process_name as a consumer across all stacks
    """
    process_resources = {}

    # Get stacks from infrastructure
    if "infrastructure" in aws_config and "stacks" in aws_config["infrastructure"]:
        stacks = aws_config["infrastructure"]["stacks"]

        # Iterate through each stack
        for stack_name, stack_config in stacks.items():
            # Check all resource types in the stack
            for resource_type, resources in stack_config.items():
                # Skip non-resource properties (like 'id')
                if resource_type == "id":
                    continue

                # Handle dictionary resources
                if isinstance(resources, dict):
                    if (
                        "consumer-processes" in resources
                        and process_name in resources["consumer-processes"]
                    ):
                        # Initialize resource type in result dict if not exists
                        if resource_type not in process_resources:
                            process_resources[resource_type] = []

                        # Add matching resource to the result
                        process_resources[resource_type].append(resources.get("id"))

                # Handle list resources (existing functionality)
                elif isinstance(resources, list):
                    # Process each resource of the current type
                    for resource in resources:
                        # Check if resource has consumer_processes and if process_name is in it
                        if (
                            "consumer-processes" in resource
                            and process_name in resource["consumer-processes"]
                        ):
                            # Initialize resource type in result dict if not exists
                            if resource_type not in process_resources:
                                process_resources[resource_type] = []

                            # Add matching resource to the result
                            process_resources[resource_type].append(resource.get("id"))

    return process_resources
