from typing import Any, Dict, List

import boto3


def get_api_secret_id(secrets):
    """
    Get the secret id which contains the str 'api_key'

    Args:
        secrets (list): List of secret identifiers

    Returns:
        str: The secret ID containing the API key
    """
    for secret_id in secrets:
        if "api-key" in secret_id:
            return secret_id
    return None


def get_source_bucket_id(buckets):
    """
    Get the bucket id which contains the str 'source'

    Args:
        buckets (list): List of bucket identifiers

    Returns:
        str: The bucket ID containing 'source' in the name
    """
    for bucket_id in buckets:
        if "source" in bucket_id.lower():
            return bucket_id
    return None


def get_secrets_from_config(config: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Extract all secrets from the AWS configuration.

    Args:
        config (Dict[str, Any]): The AWS infrastructure configuration

    Returns:
        List[Dict[str, Any]]: A list of dictionaries containing secret names and their
                             corresponding environment variable names
    """
    secrets_list = []

    for stack_name, stack_config in config.get("stacks", {}).items():
        for secret in stack_config.get("secrets", []):
            if "id" in secret and "secret-value" in secret:
                secrets_list.append(
                    {
                        "secret_name": secret.get("id"),
                        "secret_value_env": secret.get("secret-value"),
                    }
                )

    return secrets_list


def get_secret_value(secret_id, region_name=None):
    """
    Retrieve a secret value from AWS Secrets Manager

    Args:
        secret_id (str): The ID of the secret to retrieve
        region_name (str, optional): AWS region name. Defaults to None.

    Returns:
        str: The secret string value
    """
    kwargs = {}
    if region_name:
        kwargs["region_name"] = region_name

    session = boto3.session.Session()
    client = session.client(service_name="secretsmanager", **kwargs)

    try:
        secret_value_response = client.get_secret_value(SecretId=secret_id)
        return secret_value_response["SecretString"]
    except Exception as e:
        print(f"Error retrieving secret {secret_id}: {str(e)}")
        return None
