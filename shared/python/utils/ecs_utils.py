import boto3
from typing import Op<PERSON>
from botocore.exceptions import ClientError, BotoCoreError

from shared.python.utils.get_aws_config import get_aws_config


def get_image_uri_from_task_arn(task_arn: str, aws_region: str = None) -> Optional[str]:
    """
    Get the ECR image URI from an ECS task definition ARN.

    Args:
        task_arn: ARN of the ECS task definition
        aws_region: AWS region (optional, will use config if not provided)

    Returns:
        ECR image URI string if found, None otherwise

    Raises:
        ValueError: If task ARN is invalid or no image URI found
        ClientError: If AWS API call fails
        Exception: For other unexpected errors
    """
    try:
        if not task_arn:
            raise ValueError("Task ARN cannot be empty")

        # Extract region from ARN if not provided
        if aws_region is None:
            # ARN format: arn:aws:ecs:region:account-id:task-definition/name:revision
            arn_parts = task_arn.split(":")
            if len(arn_parts) >= 4:
                aws_region = arn_parts[3]
            else:
                # Fallback to config
                aws_config = get_aws_config()
                aws_region = aws_config.get("region")

        if not aws_region:
            raise ValueError("Could not determine AWS region from ARN or configuration")

        # Create ECS client
        ecs_client = boto3.client("ecs", region_name=aws_region)

        # Describe the task definition to get container details
        response = ecs_client.describe_task_definition(
            taskDefinition=task_arn
        )

        # Extract the image URI from the first container definition
        task_definition = response.get("taskDefinition", {})
        container_definitions = task_definition.get("containerDefinitions", [])

        if not container_definitions:
            raise ValueError(f"No container definitions found in task definition: {task_arn}")

        # Get the image URI from the first container (assuming single container tasks)
        image_uri = container_definitions[0].get("image")

        if not image_uri:
            raise ValueError(f"No image URI found in task definition: {task_arn}")

        return image_uri

    except ClientError as e:
        error_code = e.response.get("Error", {}).get("Code", "Unknown")
        error_message = e.response.get("Error", {}).get("Message", str(e))
        raise ClientError(
            error_response={
                "Error": {
                    "Code": error_code,
                    "Message": f"AWS ECS API error when fetching task definition '{task_arn}': {error_message}"
                }
            },
            operation_name="describe_task_definition"
        ) from e

    except BotoCoreError as e:
        raise Exception(f"AWS connection error when fetching task definition: {str(e)}") from e

    except Exception as e:
        raise Exception(f"Unexpected error getting image URI for task ARN '{task_arn}': {str(e)}") from e



