#!/usr/bin/env python3
"""
AWS instance utilities for orchestrator deployment.
Contains shared functions for managing EC2 instances and SSM parameters.
"""

import sys

import boto3
from botocore.exceptions import ClientError

from shared.python.utils.get_aws_config import get_aws_config


def get_instance_id_from_config(orchestrator_type: str) -> str:
    """
    Get the EC2 instance ID parameter name from the AWS config file.

    Args:
        orchestrator_type: Type of orchestrator (e.g., 'prefect')

    Returns:
        The EC2 instance ID parameter name
    """
    try:
        aws_config = get_aws_config()
        infra_config = aws_config.get("infrastructure", {})
        stacks_config = infra_config.get("stacks", {})
        orchestrator_config = stacks_config.get(orchestrator_type, {})
        ec2_config = orchestrator_config.get("ec2", {})
        instance_id = ec2_config.get("id")

        if not instance_id:
            print("EC2 instance ID not found in config")
            sys.exit(1)

        print(f"Using EC2 instance parameter: {instance_id}")
        return instance_id
    except Exception as e:
        print(f"Error getting EC2 instance ID from config: {e}")
        sys.exit(1)


def get_instance_id_from_parameter(ssm_client, parameter_name: str) -> str:
    """
    Get EC2 instance ID from SSM parameter.

    Args:
        ssm_client: Boto3 SSM client
        parameter_name: SSM parameter name

    Returns:
        EC2 instance ID
    """
    try:
        response = ssm_client.get_parameter(Name=parameter_name)
        instance_id = response["Parameter"]["Value"]
        print(f"Found EC2 instance ID: {instance_id}")
        return instance_id
    except Exception as e:
        print(f"Error getting instance ID from parameter {parameter_name}: {e}")
        sys.exit(1)


def get_instance_ip_address(instance_id: str, aws_region: str) -> str:
    """
    Get the IP address (public or private) of an EC2 instance.

    Args:
        instance_id: EC2 instance ID
        aws_region: AWS region

    Returns:
        IP address of the instance
    """
    try:
        ec2_client = boto3.client("ec2", region_name=aws_region)
        response = ec2_client.describe_instances(InstanceIds=[instance_id])

        instance_data = response["Reservations"][0]["Instances"][0]

        # Try to get the public IP address first
        public_ip = instance_data.get("PublicIpAddress")
        if public_ip:
            print(f"Using EC2 instance public IP: {public_ip}")
            return public_ip

        # If no public IP, use the private IP
        private_ip = instance_data.get("PrivateIpAddress")
        if private_ip:
            print(f"No public IP found. Using EC2 instance private IP: {private_ip}")
            return private_ip

        print("Error: Could not find any IP address for the EC2 instance")
        sys.exit(1)
    except ClientError as e:
        print(f"Error getting EC2 instance IP address: {e}")
        sys.exit(1)
