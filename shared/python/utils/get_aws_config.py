import os
import re
import string

import boto3
import yaml

from shared.python.utils.get_config_recursive import populate_config

# **********************************************************************


def get_aws_config():
    aws_config_path = os.getenv("AWS_CONFIG_PATH")
    print(f"Loading AWS config from {aws_config_path}")
    raw_aws_config = get_config(aws_config_path)

    env = get_env(raw_aws_config)

    aws_config_without_resource_names = populate_config(
        raw_aws_config,
        deployment_env=env.get("deployment-env"),
        region=env.get("region"),
        aws_account_id=env.get("account"),
    )

    aws_config = aws_config_without_resource_names
    aws_config["infrastructure"] = populate_yaml_paths(
        aws_config_without_resource_names.get("infrastructure")
    )

    return aws_config


# **********************************************************************


def replace_yaml_path_variables(text, **kwargs):
    pattern = r"\[([^\]]+)\]"

    def replace_match(match):
        var_name = match.group(1).replace("-", "_")
        return str(kwargs.get(var_name, f"[{var_name}]"))

    return re.sub(pattern, replace_match, text)


# **********************************************************************


def populate_yaml_paths(data, current_path=""):
    if isinstance(data, dict):
        for key, value in data.items():
            if key == "stacks":
                populate_yaml_paths(value, current_path)
            new_path = f"{current_path}-{key}" if current_path else key
            if isinstance(value, dict):
                populate_yaml_paths(value, new_path)
            elif key == "id":
                data[key] = replace_yaml_path_variables(
                    data[key], yaml_path_from_stacks=current_path
                )
            else:
                populate_yaml_paths(value, new_path)
    elif isinstance(data, list):
        for item in data:
            populate_yaml_paths(item, current_path)
    return data


# **********************************************************************


class SafeFormatter(string.Formatter):
    def get_value(self, key, args, kwargs):
        return kwargs.get(key, f"{{{key}}}")


def format_string(string, **kwargs):
    formatter = SafeFormatter()
    return formatter.format(string, **kwargs)


# **********************************************************************


def get_config_str(path_to_config_file, **kwargs):
    with open(path_to_config_file) as f:
        # Load the raw YAML content as a string
        raw_yaml = f.read()

    return format_string(raw_yaml, **kwargs)


# **********************************************************************


def get_config(path_to_config_file, **kwargs):
    with open(path_to_config_file) as f:
        # Load the raw YAML content as a string
        raw_yaml = f.read()
        formatted_str = format_string(raw_yaml, **kwargs)
        return yaml.safe_load(formatted_str)


# **********************************************************************


def get_aws_account_id():
    try:
        sts_client = boto3.client("sts")
        account_id = sts_client.get_caller_identity()["Account"]
        if account_id is None:
            print("Not able to get AWS account ID. Exiting the application.")
            exit(1)
        return account_id
    except Exception as e:
        print(f"Error retrieving AWS account ID: {e}")
        exit(1)
        return None


# **********************************************************************


def get_env(config):
    aws_account = get_aws_account_id()
    return {
        "account": aws_account,
        "region": os.getenv("AWS_REGION"),
        "deployment-env": get_aws_deployment_env(aws_account, config),
    }


# **********************************************************************


def get_aws_deployment_env(aws_account, config):
    return config.get("aws-accounts", {}).get(aws_account, {}).get("deployment-env")


# **********************************************************************
