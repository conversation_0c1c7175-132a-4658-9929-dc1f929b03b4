#!/bin/bash

# Function for error handling
handle_error() {
  echo "ERROR: $1"
  exit 1
}

# Function to get AWS account ID
get_aws_account_id() {
  # Use environment variable directly
  if [ -z "$AWS_REGION" ]; then
    handle_error "AWS_REGION environment variable is not set"
  fi

  # Get AWS account ID directly from AWS STS
  local account_id
  account_id=$(aws sts get-caller-identity --query "Account" --output text --region "$AWS_REGION") || handle_error "Failed to get AWS account ID"
  if [ -z "$account_id" ]; then
    handle_error "AWS account ID is empty"
  fi
  
  echo "$account_id"
}

# Function to get ECR URL
get_ecr_url() {
  local account_id="$1"
  local ecr_url="${account_id}.dkr.ecr.${AWS_REGION}.amazonaws.com"
  echo "$ecr_url"
}

# Function to login to ECR
login_to_ecr() {
  local ecr_url="$1"
  aws ecr get-login-password --region "$AWS_REGION" | docker login --username AWS --password-stdin "$ecr_url" || handle_error "Failed to authenticate with EC<PERSON>"
}
