from shared.python.utils.get_aws_config import get_config
from shared.python.utils.get_config_recursive import populate_config


def get_api_config(api_config_path):
    raw_api_config = get_config(api_config_path)

    api_config = populate_config(raw_api_config)

    return api_config


def get_scrape_config(scrape_config_path):
    raw_scrape_config = get_config(scrape_config_path)

    return raw_scrape_config


def substitute_placeholders(dictionary, **kwargs):
    """
    Recursively substitute placeholders in strings within a nested dictionary or list.

    Args:
        dictionary: The dictionary or list containing placeholders
        **kwargs: The values to substitute for the placeholders

    Returns:
        The dictionary with all placeholders substituted
    """

    def recursive_substitute(value):
        if isinstance(value, str):
            return value.format(**kwargs)
        elif isinstance(value, dict):
            return {k: recursive_substitute(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [recursive_substitute(item) for item in value]
        else:
            return value

    return recursive_substitute(dictionary)
