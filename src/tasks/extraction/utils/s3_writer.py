import json
from datetime import datetime

import boto3


def write_to_s3(
    bucket_name,
    prefix,
    data,
    object_name_root_word="data",
    file_extension="json",
    **kwargs,
):
    """
    Write data to S3 with partitioning and versioning.

    Args:
        bucket_name (str): The S3 bucket name
        prefix (str): The prefix for the S3 object
        data (dict/list): The data to write to S3
        object_name_root_word (str): Root word to use in object names (default: "data")
        file_extension (str): File extension to use (default: "json")
        **kwargs: Optional partitioning parameters (e.g., sport="basketball")
                  If not provided, will partition by current date (year/month/day)

    Returns:
        dict: Dictionary with the paths of the written files
    """
    s3_client = boto3.client("s3")
    current_time = datetime.now()

    # Format the timestamp for filenames
    timestamp = current_time.isoformat().replace(":", "-").replace(".", "-")

    # Create partition path
    if kwargs:
        # Custom partitioning based on provided kwargs
        partition_components = [f"{key}={value}" for key, value in kwargs.items()]
        partition_path = "/".join(partition_components)
    else:
        # Default partitioning by year/month/day
        year = current_time.strftime("%Y")
        month = current_time.strftime("%m")
        day = current_time.strftime("%d")
        partition_path = f"year={year}/month={month}/day={day}"

    # Create the full path
    base_path = f"{prefix}/{partition_path}"

    # Make sure file_extension doesn't start with a dot
    file_extension = file_extension.lstrip(".")

    # Convert data to JSON
    json_data = json.dumps(data)

    # File paths using the custom root word and extension
    timestamped_file_key = (
        f"{base_path}/{object_name_root_word}_{timestamp}.{file_extension}"
    )
    latest_file_key = f"{base_path}/{object_name_root_word}_latest.{file_extension}"

    # Determine content type based on extension
    content_type = "application/json"
    if file_extension == "csv":
        content_type = "text/csv"
    elif file_extension == "parquet":
        content_type = "application/octet-stream"
    elif file_extension == "xml":
        content_type = "application/xml"

    # Upload both versions
    s3_client.put_object(
        Bucket=bucket_name,
        Key=timestamped_file_key,
        Body=json_data,
        ContentType=content_type,
    )

    s3_client.put_object(
        Bucket=bucket_name,
        Key=latest_file_key,
        Body=json_data,
        ContentType=content_type,
    )

    return {
        "timestamped_file": timestamped_file_key,
        "latest_file": latest_file_key,
        "bucket": bucket_name,
    }
