import boto3

from shared.python.utils.secret_helpers import get_api_secret_id, get_source_bucket_id


def get_secret_value(secret_id):
    """
    Retrieve a secret value from AWS Secrets Manager

    Args:
        secret_id (str): The ID of the secret to retrieve

    Returns:
        str: The secret string value
    """
    session = boto3.session.Session()
    client = session.client(service_name="secretsmanager")
    # Try with the secret name first (as you do locally)
    try:
        response = client.get_secret_value(SecretId=secret_id)
    except Exception as e:
        # If that fails, try with the full ARN
        print(f"Failed to get secret value: {str(e)}")
        response = client.get_secret_value(SecretId=secret_id)

    # Access the secret
    return response["SecretString"]


def get_api_key(process_resources):
    """
    Get the API key from AWS Secrets Manager

    Args:
        process_resources (dict): Resources configuration for the process

    Returns:
        str: The API key
    """
    api_secret_id = get_api_secret_id(process_resources.get("secrets", []))
    if not api_secret_id:
        raise ValueError("API key secret not found in process resources")

    return get_secret_value(api_secret_id)


def get_s3_bucket_name(process_resources):
    """
    Get the S3 bucket name from the process resources

    Args:
        process_resources (dict): Resources configuration for the process

    Returns:
        str: The S3 bucket name containing 'source' in its ID
    """
    bucket_id = get_source_bucket_id(process_resources.get("buckets", []))
    if not bucket_id:
        raise ValueError("Source bucket not found in process resources")

    return bucket_id
