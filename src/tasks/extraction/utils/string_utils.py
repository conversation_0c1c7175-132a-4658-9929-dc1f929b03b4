def substitute_placeholders(dictionary, **kwargs):
    """
    Recursively substitute placeholders in strings within a nested dictionary or list.

    Args:
        dictionary: The dictionary or list containing placeholders
        **kwargs: The values to substitute for the placeholders

    Returns:
        The dictionary with all placeholders substituted
    """

    def recursive_substitute(value):
        if isinstance(value, str):
            return value.format(**kwargs)
        elif isinstance(value, dict):
            return {k: recursive_substitute(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [recursive_substitute(item) for item in value]
        else:
            return value

    return recursive_substitute(dictionary)
