import os
import json
import argparse
from typing import Dict, List, Tuple, Any
import boto3
from .api_client import call_api_function
from src.tasks.extraction.utils.s3_writer import write_to_s3


def get_requested_endpoints():
    """
    Get endpoints requested via command line arguments.

    Returns:
        List of endpoint names or empty list if all endpoints are requested
    """
    parser = argparse.ArgumentParser(description="Extract data from API")
    parser.add_argument(
        "--endpoints",
        nargs="*",
        help="Specific endpoints to process (empty for all endpoints)",
    )
    args = parser.parse_args()
    return args.endpoints or []


def filter_endpoints(
    endpoints: Dict[str, Any], requested_endpoints: List[str]
) -> Tuple[Dict[str, Any], str]:
    """
    Filter endpoints based on user request.

    Args:
        endpoints: Dictionary of available endpoints
        requested_endpoints: List of requested endpoint names

    Returns:
        Tuple containing filtered endpoints dictionary and log message
    """
    if not requested_endpoints:
        return endpoints, f"Processing all {len(endpoints)} endpoints"

    filtered = {
        name: config
        for name, config in endpoints.items()
        if name in requested_endpoints
    }

    if not filtered:
        return {}, "No valid endpoints specified"

    return filtered, f"Processing {len(filtered)} of {len(endpoints)} endpoints"


def save_data_to_s3(data, storage_config, endpoint_name):
    """
    Save data to S3 bucket.

    Args:
        data: Data to save
        storage_config: Storage configuration
        endpoint_name: Name of the endpoint
    """
    # This is a placeholder - implement actual S3 upload logic
    prefix = storage_config.get("prefix")
    file_path = f"{prefix}/{endpoint_name}.json"

    # Create local directories if needed
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    # Save locally for now
    with open(file_path, "w") as f:
        json.dump(data, f)

    print(f"Data saved to {file_path}")
    # In production, would use boto3 to upload to S3


def process_endpoints(api_client, endpoints: Dict[str, Any]):
    """
    Process each endpoint and store the results.

    Args:
        api_client: API client instance
        endpoints: Dictionary of endpoints to process

    Returns:
        Dict mapping endpoint names to their response data
    """
    results = {}

    for endpoint_name, config in endpoints.items():
        print(f"Processing endpoint: {endpoint_name}")

        try:
            # Make the API request
            response = api_client.make_request(
                method=config.get("method", "GET"),
                endpoint=config.get("url", ""),
                params=config.get("params", {}),
            )

            # Store result in dictionary
            results[endpoint_name] = {
                "data": response,
                "storage_config": config.get("storage", {}),
            }

            print(f"Successfully retrieved data for {endpoint_name}")

        except Exception as e:
            print(f"Error processing endpoint {endpoint_name}: {str(e)}")

    return results


def save_endpoint_results(results, bucket_name):
    """
    Save endpoint results to S3.

    Args:
        results (dict): Dictionary with endpoint results
        bucket_name (str): S3 bucket name

    Returns:
        list: List of S3 paths where the data was saved
    """
    saved_locations = []

    for endpoint_name, endpoint_data in results.items():
        if endpoint_data and "data" in endpoint_data:
            # Get storage configuration
            storage_config = endpoint_data.get("storage_config", {})

            # Get prefix from storage_config or use default
            prefix = storage_config.get("prefix")

            # Get file extension from storage_config or use default
            file_extension = storage_config.get("file_extension", "json")

            s3_info = write_to_s3(
                bucket_name=bucket_name,
                prefix=prefix,
                data=endpoint_data["data"],
                object_name_root_word=endpoint_name,
                file_extension=file_extension,
            )
            saved_locations.append(s3_info)
            print(
                f"Saved {endpoint_name} data to {s3_info['timestamped_file']} and {s3_info['latest_file']}"
            )

    return saved_locations
