import requests
import time
from typing import Dict, Any


class RequestsApiClient:
    """API client using the requests library"""

    def __init__(
        self,
        base_url: str,
        headers: Dict[str, str],
        timeout: int = 30,
        retry_attempts: int = 3,
    ):
        """
        Initialize the API client.

        Args:
            base_url: Base URL for the API
            headers: HTTP headers to include in requests
            timeout: Request timeout in seconds
            retry_attempts: Number of retry attempts for failed requests
        """
        self.base_url = base_url
        self.headers = headers
        self.timeout = timeout
        self.retry_attempts = retry_attempts

    def make_request(self, method: str, endpoint: str, params: Dict[str, Any] = None):
        """
        Make an API request with retry logic.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint path
            params: Query parameters

        Returns:
            Response data in JSON format
        """
        url = f"{self.base_url}{endpoint}"

        for attempt in range(self.retry_attempts):
            try:
                response = requests.request(
                    method=method,
                    url=url,
                    headers=self.headers,
                    params=params,
                    timeout=self.timeout,
                )

                response.raise_for_status()
                return response.json()

            except (requests.RequestException, requests.Timeout) as e:
                if attempt == self.retry_attempts - 1:
                    raise Exception(
                        f"API request failed after {self.retry_attempts} attempts: {str(e)}"
                    )
                time.sleep(2**attempt)  # Exponential backoff


def initialize_api(api_config: Dict[str, Any]):
    """
    Initialize the API client based on configuration.

    Args:
        api_config: API configuration dictionary

    Returns:
        API client instance
    """
    api_settings = api_config.get("api", {})

    return RequestsApiClient(
        base_url=api_settings.get("base_url"),
        headers=api_settings.get("headers", {}),
        timeout=api_settings.get("timeout", 30),
        retry_attempts=api_settings.get("retry_attempts", 3),
    )


def call_api_function(api_instance, function_path, **kwargs):
    """
    Dynamically call a function on the API instance using a dot-notation path.

    Args:
        api_instance: The API instance
        function_path (str): String representing the function path (e.g., "nba.teams.list")
        kwargs: Optional arguments to pass to the function

    Returns:
        The result of the API call
    """
    parts = function_path.split(".")
    current = api_instance

    # Navigate through the object attributes
    for part in parts[:-1]:
        current = getattr(current, part)

    # Call the final method
    method = getattr(current, parts[-1])
    return method(**kwargs)
