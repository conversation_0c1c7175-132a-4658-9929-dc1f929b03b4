{
    "name": "betting_exchange",
    "image": "677276111650.dkr.ecr.us-east-2.amazonaws.com/devcontainer:devcontainer-latest",
    "initializeCommand": "chmod +x ./.devcontainer/scripts/devcontainer_initialize.sh && ./.devcontainer/scripts/devcontainer_initialize.sh",
    "forwardPorts": [
        8080,
        8000,
        9000,
        5432,
        4200,
        4201,
        3000
    ],
    "runArgs": [
        "--name=betting_exchange",
        "--privileged", // For docker in docker
        "--add-host=host.docker.internal:host-gateway",
        // ls -l /var/run/docker.sock showed that group 1001 from wsl had access, so this adds vscode user to that group.
        "--group-add=1001",
        // "--network=prefect-network"
    ],
    "customizations": {
        "vscode": {
            "settings": {
                "python.venvPath": "/betting_exchange/.venv"
            },
            "extensions": [
                "ms-azuretools.vscode-docker",
                "ms-python.python",
                "ms-python.vscode-pylance",
                "charliermarsh.ruff",
                "ms-toolsai.jupyter",
                "yzhang.markdown-all-in-one",
                "njpwerner.autodocstring",
                "atlassian.atlascode",
                "mark-wiemer.vscode-autohotkey-plus-plus",
                "docsmsft.docs-yaml",
                "nachocab.select-paragraph",
                "dbankier.vscode-quick-select",
                "amazonwebservices.aws-toolkit-vscode",
                "ow.vscode-subword-navigation",
                "ziyasal.vscode-open-in-github",
                "mhutchie.git-graph",
                "geddski.macros",
                "streetsidesoftware.code-spell-checker",
                "github.copilot",
                "github.copilot-chat",
                "augment.vscode-augment",
                "bateleurio.vscode-combine-scripts",
                "bibhasdn.unique-lines",
                "boto3typed.boto3-ide",
                "christian-kohler.path-intellisense",
                "david-rickard.git-diff-and-merge-tool",
                "donjayamanne.python-environment-manager",
                "dvirtz.parquet-viewer",
                "eamodio.gitlens",
                "eplak.vscode-uppercasesql",
                "github.vscode-pull-request-github",
                "gruntfuggly.todo-tree",
                "haberdashpi.vscode-select-by-indent",
                "hbenl.vscode-test-explorer",
                "littlefoxteam.vscode-python-test-adapter",
                "jkjustjoshing.vscode-text-pastry",
                "kevinrose.vsc-python-indent",
                "ms-toolsai.datawrangler",
                "ms-vscode.sublime-keybindings",
                "pnp.polacode",
                "redhat.vscode-yaml",
                "rogalmic.bash-debug",
                "rubymaniac.vscode-paste-and-indent",
                "stackbreak.comment-divider",
                "timonwong.shellcheck",
                "visualstudioexptteam.vscodeintellicode",
                "ionutvmi.path-autocomplete",
                "ms-python.debugpy",
                "pkief.material-icon-theme",
                "rioj7.select-by",
                "github.vscode-github-actions",
                "shakram02.bash-beautify"
            ]
        }
    },
    "postCreateCommand": "chmod +x /betting_exchange/.devcontainer/scripts/*  && /bin/bash -c '/betting_exchange/.devcontainer/scripts/post_create_command.sh'",
    "remoteEnv": {
        "PYTHONPATH": "/betting_exchange/",
        "AWS_REGION": "us-east-2",
        // "SSH_AUTH_SOCK": "/ssh-auth.sock",
        "HOST_HOME": "${localEnv:HOME}", // Used to mount the .aws to containers in the devcontainer
        "PATH": "${containerEnv:PATH}:/home/<USER>/.local/bin"
    },
    "shutdownAction": "stopContainer",
    "workspaceFolder": "/betting_exchange",
    "workspaceMount": "source=${localWorkspaceFolder},target=/betting_exchange,type=bind,consistency=cached",
    "remoteUser": "vscode",
    "mounts": [
        "source=${localEnv:HOME}${localEnv:USERPROFILE}/.aws,target=/home/<USER>/.aws,type=bind,consistency=cached",
        "source=${localEnv:HOME}${localEnv:USERPROFILE}/.ssh,target=/home/<USER>/.ssh,type=bind,consistency=cached",
        "source=${localEnv:SSH_AUTH_SOCK},target=/ssh-auth.sock,type=bind,consistency=cached",
        "source=/var/run/docker.sock,target=/var/run/docker.sock,type=bind,consistency=delegated"
    ],
    "features": {
        "docker-in-docker": {
            "version": "latest",
            "moby": true
        }
    }
}