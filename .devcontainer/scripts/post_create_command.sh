#!/bin/bash
# .devcontainer/scripts/post_create_command.sh

# Get the directory of the current script
SCRIPT_DIR="$(dirname "$0")"

# Define the script to run
DEPENDENCIES_SCRIPT="$SCRIPT_DIR/python_env_dependencies.sh"

# Check if the script exists
if [[ -f "$DEPENDENCIES_SCRIPT" ]]; then
    echo "Running $DEPENDENCIES_SCRIPT..."
    bash "$DEPENDENCIES_SCRIPT"
else
    echo "Error: $DEPENDENCIES_SCRIPT not found!"
    exit 1
fi

# Export AWS secrets as environment variables
EXPORT_SECRETS_SCRIPT="$SCRIPT_DIR/export_aws_secrets.py"
if [[ -f "$EXPORT_SECRETS_SCRIPT" ]]; then
    echo "Exporting AWS secrets as environment variables..."
    python "$EXPORT_SECRETS_SCRIPT"
else
    echo "Warning: $EXPORT_SECRETS_SCRIPT not found. Skipping secret export."
fi

# Add AWS access keys to .bashrc if not already present
if ! grep -q "AWS_ACCESS_KEY_ID" ~/.bashrc; then
    echo "Adding AWS environment variables to .bashrc..."
    
    # Get the actual AWS credential values
    AWS_ACCESS_KEY=$(aws configure get aws_access_key_id --profile dev)
    AWS_SECRET_KEY=$(aws configure get aws_secret_access_key --profile dev)
    
    # Check if we successfully retrieved the credentials
    if [[ -n "$AWS_ACCESS_KEY" && -n "$AWS_SECRET_KEY" ]]; then
        echo "" >> ~/.bashrc
        echo "export AWS_ACCESS_KEY_ID='$AWS_ACCESS_KEY'" >> ~/.bashrc
        echo "export AWS_SECRET_ACCESS_KEY='$AWS_SECRET_KEY'" >> ~/.bashrc
        echo "AWS credentials added to .bashrc successfully"
    else
        echo "Warning: Could not retrieve AWS credentials from profile 'dev'"
    fi
else
    echo "AWS environment variables already present in .bashrc"
fi

cp /workspace/package-lock.json /betting_exchange
cp /workspace/package.json /betting_exchange