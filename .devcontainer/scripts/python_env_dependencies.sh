#!/bin/bash

# Get the directory of the script
SCRIPT_DIR="$(dirname "$0")"
ENV_LIST_FILE="$SCRIPT_DIR/venv_envs.txt"
VENV_DIR="/betting_exchange/.venv"  # Directory to store all virtual environments

# Create the venv directory if it doesn't exist
mkdir -p "$VENV_DIR"

# Clear the file before writing new environments
> "$ENV_LIST_FILE"

# Find all requirements* files
find . -name 'requirements*' -type f | while read -r req_file; do
    # Skip requirements files that are in site-packages (or similar directories)
    if echo "$req_file" | grep -q '/site-packages/'; then
        echo "Skipping $req_file as it is in a site-packages directory."
        continue
    fi

    # Get the relative path (removing `./` prefix)
    rel_path=$(realpath --relative-to=$(pwd) "$req_file")
    
    # Convert slashes to underscores to ensure a valid venv env name
    env_name=$(dirname "$rel_path" | tr '/' '_')

    # Define the full path to the virtual environment
    venv_path="$VENV_DIR/$env_name"

    # Check if venv environment exists
    if [ -d "$venv_path" ]; then
        echo "Virtual environment '$env_name' already exists. Activating and installing dependencies..."

        # Activate the environment and install requirements
        source "$venv_path/bin/activate"
        pip install -r "$req_file"
        deactivate
    else
        echo "Creating virtual environment: $env_name"

        # Create the virtual environment
        python3 -m venv "$venv_path"

        echo "Installing dependencies from $req_file into $env_name"

        # Activate the environment and install the requirements
        source "$venv_path/bin/activate"
        pip install -r "$req_file"
        deactivate
    fi

    # Append the activation command for the environment to the file (avoid duplicates)
    if ! grep -q "^source $venv_path/bin/activate$" "$ENV_LIST_FILE"; then
        echo "source $venv_path/bin/activate" >> "$ENV_LIST_FILE"
    fi

done

echo "All virtual environments are listed in $ENV_LIST_FILE with activation commands."