#!/bin/bash
PROFILE="dev"

# Get MFA Serial from AWS config
SERIAL=$(aws configure get profile.$PROFILE.mfa_serial)
if [ -z "$SERIAL" ]; then
    echo "MFA Serial not found in ~/.aws/config for profile $PROFILE"
    exit 1
fi

# Get AWS region from config
AWS_REGION=$(aws configure get profile.$PROFILE.region)
if [ -z "$AWS_REGION" ]; then
    echo "AWS region not found in ~/.aws/config for profile $PROFILE"
    exit 1
fi

# Prompt user for MFA Code
read -p "Enter MFA Code: " MFA_CODE

# Get temporary session token
CREDENTIALS=$(aws sts get-session-token --profile $PROFILE --serial-number $SERIAL --token-code $MFA_CODE --output json)
if [ $? -ne 0 ]; then
    echo "Failed to get session token"
    exit 1
fi

# Export temporary AWS credentials
# shellcheck disable=SC2155
export AWS_ACCESS_KEY_ID=$(echo $CREDENTIALS | jq -r .Credentials.AccessKeyId)
# shellcheck disable=SC2155
export AWS_SECRET_ACCESS_KEY=$(echo $CREDENTIALS | jq -r .Credentials.SecretAccessKey)
# shellcheck disable=SC2155
export AWS_SESSION_TOKEN=$(echo $CREDENTIALS | jq -r .Credentials.SessionToken)
export AWS_DEFAULT_REGION=$AWS_REGION

echo "Temporary AWS credentials set for MFA-authenticated session in region: $AWS_REGION."
