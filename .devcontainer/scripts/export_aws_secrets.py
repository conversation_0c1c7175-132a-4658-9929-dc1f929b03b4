#!/usr/bin/env python3
"""
<PERSON>ript to export AWS secrets as environment variables.

This script reads the AWS config file, retrieves all secrets defined in it,
and exports them as environment variables.
"""

import os
import sys
import subprocess
from typing import Dict, Any, List

# Add the project root to the Python path
sys.path.insert(0, "/betting_exchange")

from shared.python.utils.get_aws_config import get_aws_config
from shared.python.utils.secret_helpers import get_secrets_from_config, get_secret_value


def export_secrets_as_env_vars(config: Dict[str, Any]) -> int:
    """
    Export all secrets defined in the AWS config as environment variables.

    Args:
        config (Dict[str, Any]): The AWS infrastructure configuration

    Returns:
        int: 0 if successful, non-zero otherwise
    """
    # Get all secrets from the configuration
    secrets = get_secrets_from_config(config)
    
    if not secrets:
        print("No secrets found in the configuration.")
        return 0
    
    print(f"Found {len(secrets)} secrets in the configuration.")
    
    # Get the region from the config
    region = config.get("region")
    
    # Export each secret as an environment variable
    success_count = 0
    for secret in secrets:
        secret_name = secret["secret_name"]
        env_var_name = secret["secret_value_env"]
        
        # Retrieve the secret value from AWS Secrets Manager
        secret_value = get_secret_value(secret_name, region)
        
        if secret_value:
            # Export the secret as an environment variable
            # Using subprocess to set environment variables in the parent shell
            export_cmd = f'echo "export {env_var_name}=\'{secret_value}\'" >> ~/.bashrc'
            try:
                subprocess.run(export_cmd, shell=True, check=True)
                print(f"Exported secret '{secret_name}' as environment variable '{env_var_name}'")
                success_count += 1
            except subprocess.CalledProcessError as e:
                print(f"Failed to export secret '{secret_name}': {str(e)}")
        else:
            print(f"Failed to retrieve secret '{secret_name}'")
    
    print(f"\nSummary: Exported {success_count} out of {len(secrets)} secrets successfully.")
    
    # Source the bashrc file to make the environment variables available in the current session
    subprocess.run("source ~/.bashrc", shell=True, executable="/bin/bash")
    
    return 0 if success_count == len(secrets) else 1


def main():
    """
    Main function to export AWS secrets as environment variables.
    """
    # Set the AWS_CONFIG_PATH environment variable if not already set
    if not os.getenv("AWS_CONFIG_PATH"):
        os.environ["AWS_CONFIG_PATH"] = "/betting_exchange/shared/config/aws_config.yaml"
    
    # Get the AWS config
    aws_config = get_aws_config()
    aws_infra_config = aws_config.get("infrastructure")
    
    # Export secrets as environment variables
    return export_secrets_as_env_vars(aws_infra_config)


if __name__ == "__main__":
    sys.exit(main())
