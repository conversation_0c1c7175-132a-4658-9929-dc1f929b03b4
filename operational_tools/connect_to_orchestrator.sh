#!/bin/bash

# Examples of how to run this script:
#
# 1. Connect to Orchestrator server with port forwarding (default mode):
#    operational_tools/connect_to_orchestrator.sh
#
# 2. Connect to Orchestrator server with interactive terminal:
#    operational_tools/connect_to_orchestrator.sh -i
#
# 3. Test connection to bastion host only:
#    operational_tools/connect_to_orchestrator.sh -t
#
# 4. Combine options (e.g., interactive mode and test bastion):
#    operational_tools/connect_to_orchestrator.sh -i -t

# Set AWS region
AWS_REGION=${AWS_REGION:-"us-east-2"}
SSH_KEY_PATH="/home/<USER>/.ssh/dev-bastion-key-pair.pem"

# Parse command line arguments
INTERACTIVE_MODE=false
TEST_BASTION_ONLY=false
while getopts "it" opt; do
  case $opt in
    i)
      INTERACTIVE_MODE=true
      ;;
    t)
      TEST_BASTION_ONLY=true
      ;;
    \?)
      echo "Invalid option: -$OPTARG" >&2
      exit 1
      ;;
  esac
done

echo "Finding bastion host and orchestrator server in region $AWS_REGION..."

# Get bastion host public IP
BASTION_PUBLIC_IP=$(aws ec2 describe-instances \
  --region $AWS_REGION \
  --filters "Name=tag:Name,Values=*bastion*" "Name=instance-state-name,Values=running" \
  --query "Reservations[0].Instances[0].PublicIpAddress" \
  --output text)

if [ -z "$BASTION_PUBLIC_IP" ] || [ "$BASTION_PUBLIC_IP" == "None" ]; then
  echo "Error: Could not find running bastion host instance"
  exit 1
fi

echo "Found bastion host with IP: $BASTION_PUBLIC_IP"

# Get orchestrator server private IP
ORCHESTRATOR_PRIVATE_IP=$(aws ec2 describe-instances \
  --region $AWS_REGION \
  --filters "Name=tag:Name,Values=*prefect*" "Name=instance-state-name,Values=running" \
  --query "Reservations[0].Instances[0].PrivateIpAddress" \
  --output text)

if [ -z "$ORCHESTRATOR_PRIVATE_IP" ] || [ "$ORCHESTRATOR_PRIVATE_IP" == "None" ]; then
  echo "Error: Could not find running orchestrator server instance"
  exit 1
fi

echo "Found orchestrator server with private IP: $ORCHESTRATOR_PRIVATE_IP"

# Ensure SSH key has correct permissions
chmod 400 $SSH_KEY_PATH

# Check if SSH agent is running and restart it if needed
echo "Ensuring SSH agent is running properly..."
pkill ssh-agent || true
eval $(ssh-agent -s)
echo "SSH agent started with PID $SSH_AGENT_PID"

# Add the SSH key to the agent with more verbose output
echo "Adding SSH key to agent..."
ssh-add -v $SSH_KEY_PATH
if [ $? -ne 0 ]; then
  echo "Failed to add key to SSH agent. Checking key permissions..."
  ls -la $SSH_KEY_PATH
  echo "Trying to fix permissions..."
  chmod 600 $SSH_KEY_PATH
  ssh-add -v $SSH_KEY_PATH
  if [ $? -ne 0 ]; then
    echo "Still failed to add key. Continuing without agent, connection may fail."
  fi
else
  echo "Key added successfully to SSH agent."
fi

if [ "$TEST_BASTION_ONLY" = true ]; then
  # Test connection to bastion host only
  echo "Testing connection to bastion host only..."
  echo "Press Ctrl+D or type 'exit' to close the connection"
  
  # SSH to bastion host
  ssh -i $SSH_KEY_PATH ec2-user@$BASTION_PUBLIC_IP
elif [ "$INTERACTIVE_MODE" = true ]; then
  # Connect with interactive terminal
  echo "Connecting to orchestrator server with interactive terminal..."
  echo "Press Ctrl+D or type 'exit' to close the connection"
  
  # SSH to orchestrator server through bastion with interactive terminal and verbose output
  ssh -i $SSH_KEY_PATH -J ec2-user@$BASTION_PUBLIC_IP ec2-user@$ORCHESTRATOR_PRIVATE_IP
else
  # Set up SSH tunnel and connect to orchestrator server through bastion
  echo "Connecting to orchestrator server through bastion host..."
  echo "Use http://localhost:4201 to access Orchestrator UI"
  echo "Press Ctrl+C to close the connection"
  
  # SSH to orchestrator server through bastion with port forwarding for Orchestrator UI
  ssh -i $SSH_KEY_PATH -J ec2-user@$BASTION_PUBLIC_IP -L 4201:localhost:4200 ec2-user@$ORCHESTRATOR_PRIVATE_IP
fi
