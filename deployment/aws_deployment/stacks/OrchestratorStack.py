from aws_cdk import Stack
from aws_cdk import aws_ec2 as ec2
from constructs import Construct

from deployment.aws_deployment.resource_utils.ec2_utils import (
    create_ec2_instance_from_config,
    create_ec2_role_from_config,
    create_ec2_security_group_from_config,
)
from deployment.aws_deployment.resource_utils.extraction_utils import (
    create_secrets_from_config,
)
from deployment.aws_deployment.resource_utils.lambda_utils import (
    create_lambda_from_config,
    create_lambda_role_from_config,
)


class OrchestratorStack(Stack):
    def __init__(
        self,
        scope: Construct,
        aws_config: dict,
        deployment_env,
        vpc: ec2.Vpc = None,
        security_groups: dict = None,  # Used to find the bastion security group
        region: str = None,
        **kwargs,
    ) -> None:
        """
        Initialize a new Orchestrator Stack.

        This stack is responsible for creating AWS resources for Orchestrator,
        including secrets and EC2 instances.

        Args:
            scope: The CDK construct scope
            aws_config: Configuration for the Orchestrator stack
            vpc: VPC from the network stack (optional)
            security_groups: Security groups from the network stack (optional)
            **kwargs: Additional arguments to pass to the Stack constructor
        """
        # Initialize the stack with the ID from config
        stack_id = aws_config.get("id")
        super().__init__(scope, stack_id, **kwargs)

        # Create secrets from config
        self.secrets = create_secrets_from_config(
            self,
            secrets_config=aws_config.get("secrets", []),
        )

        # Get EC2 configuration
        ec2_config = aws_config.get("ec2")

        # Create EC2 instance if configuration is provided and VPC is available
        if ec2_config and vpc:
            # Create security group for EC2 instance
            sg_config = ec2_config.get("security-group")
            if sg_config:
                self.ec2_security_group = create_ec2_security_group_from_config(
                    self,
                    sg_config=sg_config,
                    vpc=vpc,
                )

            # Create IAM role for EC2 instance
            role_config = ec2_config.get("iam-role")
            if role_config:
                # Extract secret ARNs for role permissions
                secret_arns = [secret.secret_arn for secret in self.secrets.values()]

                self.ec2_role = create_ec2_role_from_config(
                    self,
                    role_config=role_config,
                    secrets_arns=secret_arns,
                )

            # Find the bastion security group if it exists
            bastion_sg = None
            if security_groups:
                for sg_id, sg in security_groups.items():
                    if "bastion" in sg_id:
                        bastion_sg = sg
                        break

            # Add ingress rule for SSH from the bastion host if it exists
            if bastion_sg:
                self.ec2_security_group.add_ingress_rule(
                    ec2.Peer.security_group_id(bastion_sg.security_group_id),
                    ec2.Port.tcp(22),
                    "Allow SSH access from bastion host",
                )

            # Add ingress rules for Lambda to EC2 communication
            # Allow HTTP traffic from Lambda (port 4200 for Prefect API)
            # self.ec2_security_group.add_ingress_rule(
            #     ec2.Peer.security_group_id(self.ec2_security_group.security_group_id),
            #     ec2.Port.tcp(4200),
            #     "Allow Lambda access to Prefect API on EC2",
            # )

            # # Allow HTTPS traffic if needed
            # self.ec2_security_group.add_ingress_rule(
            #     ec2.Peer.security_group_id(self.ec2_security_group.security_group_id),
            #     ec2.Port.tcp(443),
            #     "Allow HTTPS traffic from Lambda to EC2",
            # )

            # # Allow HTTP traffic if needed
            # self.ec2_security_group.add_ingress_rule(
            #     ec2.Peer.security_group_id(self.ec2_security_group.security_group_id),
            #     ec2.Port.tcp(80),
            #     "Allow HTTP traffic from Lambda to EC2",
            # )

            # Create EC2 instance
            self.ec2_instance = create_ec2_instance_from_config(
                self,
                ec2_config=ec2_config,
                vpc=vpc,
                security_groups=[self.ec2_security_group],
                role=self.ec2_role,
                key_name=f"{deployment_env}-bastion-key-pair",
                secrets_config=aws_config.get("secrets", []),
            )

        # Get Lambda configuration
        lambda_config = aws_config.get("lambda")

        # Create Lambda function if configuration is provided
        if lambda_config:
            # Create IAM role for Lambda function
            lambda_role_config = lambda_config.get("iam-role")
            if lambda_role_config:
                self.lambda_role = create_lambda_role_from_config(
                    self,
                    role_config=lambda_role_config,
                )

                # Get orchestrator IP from EC2 instance
                orchestrator_ip = (
                    self.ec2_instance.instance_private_ip
                    if hasattr(self, "ec2_instance")
                    else None
                )

                # # Create separate security group for Lambda
                # self.lambda_security_group = ec2.SecurityGroup(
                #     self,
                #     f"{stack_id}-lambda-sg",
                #     security_group_name=f"{stack_id}-lambda-sg",
                #     vpc=vpc,
                #     description="Security group for Lambda functions",
                #     allow_all_outbound=True,
                # )

                # # Now create ingress rules between different security groups
                # self.ec2_security_group.add_ingress_rule(
                #     ec2.Peer.security_group_id(
                #         self.lambda_security_group.security_group_id
                #     ),
                #     ec2.Port.tcp(4200),
                #     "Allow Lambda access to Prefect API on EC2",
                # )

                # Use the Lambda security group when creating the Lambda function
                self.lambda_function = create_lambda_from_config(
                    self,
                    lambda_config=lambda_config,
                    role=self.lambda_role,
                    deployment_env=deployment_env,
                    orchestrator_ip=orchestrator_ip,
                    region=region,
                    # vpc=vpc,
                    # security_group=self.lambda_security_group,  # Use Lambda-specific SG
                )
