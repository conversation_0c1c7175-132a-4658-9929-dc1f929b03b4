from typing import Dict

from aws_cdk import Stack
from aws_cdk import aws_ec2 as ec2
from constructs import Construct

from deployment.aws_deployment.resource_utils.extraction_utils import (
    create_buckets_from_config,
    create_secrets_from_config,
)
from deployment.aws_deployment.resource_utils.iam_utils import (
    create_iam_roles_from_config,
)
from deployment.aws_deployment.resource_utils.task_utils import (
    create_ecs_cluster,
    create_ecs_tasks_from_config,
)


class ExtractionStack(Stack):
    def __init__(
        self,
        scope: Construct,
        aws_config: dict,
        deployment_env,
        vpc: ec2.Vpc,
        security_groups: Dict[str, ec2.SecurityGroup],
        orchestrator_ip: str = None,
        **kwargs,
    ) -> None:
        super().__init__(scope, aws_config.get("id"), **kwargs)

        # Create buckets first
        self.buckets = create_buckets_from_config(
            self,
            bucket_config=aws_config.get("buckets"),
        )

        # Create secrets next
        self.secrets = create_secrets_from_config(
            self,
            secrets_config=aws_config.get("secrets"),
        )

        # Create IAM roles with access to both secrets and buckets
        self.iam_roles = create_iam_roles_from_config(
            self,
            iam_config=aws_config.get("iam"),
            deployment_env=deployment_env,
            secrets=self.secrets,
            buckets=self.buckets,
        )

        # Create ECS cluster
        self.cluster = create_ecs_cluster(
            self,
            vpc=vpc,
            cluster_config=aws_config.get("cluster", {}),
        )

        # Create ECS Fargate tasks with IAM roles
        self.task_definitions = create_ecs_tasks_from_config(
            self,
            task_config=aws_config.get("tasks", []),
            iam_roles=self.iam_roles,
            orchestrator_ip=orchestrator_ip,
        )
