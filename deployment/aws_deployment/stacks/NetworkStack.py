from aws_cdk import Stack
from aws_cdk import aws_ec2 as ec2
from aws_cdk import aws_iam as iam
from constructs import Construct

from deployment.aws_deployment.resource_utils.ec2_utils import (
    create_ec2_instance_from_config,
    create_ec2_role_from_config,
    create_ec2_security_group_from_config,
)
from deployment.aws_deployment.resource_utils.network_utils import (
    create_a_records_from_config,
    create_private_hosted_zone_from_config,
    create_security_groups_from_config,
    create_vpc_endpoints_from_config,
    create_vpc_from_config,
)


class NetworkStack(Stack):
    def __init__(
        self, scope: Construct, aws_config: dict, deployment_env, **kwargs
    ) -> None:
        super().__init__(scope, aws_config.get("id"), **kwargs)

        # Create VPC with public and private subnets
        self.vpc = create_vpc_from_config(self, vpc_config=aws_config.get("vpc"))

        # Create security groups
        self.security_groups = create_security_groups_from_config(
            self, security_group_config=aws_config.get("security-groups"), vpc=self.vpc
        )

        # Create VPC endpoints to allow private subnet to access AWS services without NAT
        self.vpc_endpoints = create_vpc_endpoints_from_config(
            self,
            vpc_endpoint_config=aws_config.get("vpc-endpoints"),
            vpc=self.vpc,
            security_groups=self.security_groups,
        )

        # Create EC2 instance if configuration is provided
        ec2_config = aws_config.get("ec2")
        if ec2_config:
            # Create IAM role for EC2 instance
            role_config = ec2_config.get("iam-role")
            if role_config:
                self.ec2_role = create_ec2_role_from_config(
                    self,
                    role_config=role_config,
                )

            # Get or create the security group for the EC2 instance
            sg_config = ec2_config.get("security-group")
            ec2_sg = None

            if sg_config:
                sg_id = sg_config.get("id")
                if sg_id in self.security_groups:
                    # Use existing security group if it's already created
                    ec2_sg = self.security_groups[sg_id]
                else:
                    # Create a new security group from the EC2 config
                    ec2_sg = create_ec2_security_group_from_config(
                        self,
                        sg_config=sg_config,
                        vpc=self.vpc,
                    )
                    # Add it to our security groups dictionary
                    self.security_groups[sg_id] = ec2_sg

            # Create the EC2 instance
            if ec2_sg:
                self.ec2_instance = ec2.Instance(
                    self,
                    ec2_config.get("id"),
                    instance_type=ec2.InstanceType(
                        ec2_config.get("instance-type", "t2.micro")
                    ),
                    machine_image=ec2.MachineImage.latest_amazon_linux2(),
                    vpc=self.vpc,
                    vpc_subnets=ec2.SubnetSelection(subnet_type=ec2.SubnetType.PUBLIC),
                    security_group=ec2_sg,
                    role=self.ec2_role,
                    key_pair=ec2.KeyPair.from_key_pair_name(
                        self, "ImportedKeyPair", f"{deployment_env}-bastion-key-pair"
                    ),
                    allow_all_outbound=True,
                )

                # Add specific ingress rule for SSH from your IP
                ec2_sg.add_ingress_rule(
                    ec2.Peer.any_ipv4(),  # Replace with your specific IP for better security
                    ec2.Port.tcp(22),
                    "Allow SSH access from your IP",
                )

        # Create Route 53 private hosted zone if configuration is provided
        hosted_zone_config = aws_config.get("private-hosted-zone")
        if hosted_zone_config:
            self.private_hosted_zone = create_private_hosted_zone_from_config(
                self, hosted_zone_config=hosted_zone_config, vpc=self.vpc
            )

            # Create A record for the bastion EC2 instance if it exists
            if hasattr(self, "ec2_instance"):
                # Get A record configuration for this stack's EC2 instance
                a_records_config = aws_config.get("a-records", [])
                if a_records_config:
                    # Create dictionary with this stack's EC2 instance
                    ec2_instances = {
                        aws_config.get("ec2", {}).get("id"): self.ec2_instance
                    }

                    self.a_records = create_a_records_from_config(
                        self,
                        a_records_config=a_records_config,
                        hosted_zone=self.private_hosted_zone,
                        ec2_instances=ec2_instances,
                    )
