from aws_cdk import Stack
from aws_cdk import aws_ec2 as ec2
from constructs import Construct

from deployment.aws_deployment.resource_utils.network_utils import (
    create_a_records_from_config,
    create_private_hosted_zone_from_config,
)


class Route53Stack(Stack):
    def __init__(
        self,
        scope: Construct,
        aws_config: dict,
        vpc: ec2.Vpc,
        ec2_instances: dict = None,
        **kwargs,
    ) -> None:
        """
        Initialize a new Route53 Stack.

        This stack is responsible for creating Route 53 private hosted zones
        and A records for EC2 instances.

        Args:
            scope: The CDK construct scope
            aws_config: Configuration for the Route53 stack
            vpc: VPC from the network stack
            ec2_instances: Dictionary of EC2 instances from other stacks
            **kwargs: Additional arguments to pass to the Stack constructor
        """
        # Initialize the stack with the ID from config
        stack_id = aws_config.get("id")
        super().__init__(scope, stack_id, **kwargs)

        # Create private hosted zone if configuration is provided
        hosted_zone_config = aws_config.get("private-hosted-zone")
        if hosted_zone_config:
            self.private_hosted_zone = create_private_hosted_zone_from_config(
                self, hosted_zone_config=hosted_zone_config, vpc=vpc
            )

            # Create A records if configuration is provided
            a_records_config = aws_config.get("a-records", [])
            if a_records_config and ec2_instances:
                self.a_records = create_a_records_from_config(
                    self,
                    a_records_config=a_records_config,
                    hosted_zone=self.private_hosted_zone,
                    ec2_instances=ec2_instances,
                )
