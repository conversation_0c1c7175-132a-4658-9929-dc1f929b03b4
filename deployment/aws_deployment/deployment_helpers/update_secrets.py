import os
from typing import Any, Dict, Optional

import boto3

from shared.python.utils.get_aws_config import get_aws_config
from shared.python.utils.secret_helpers import get_secrets_from_config

# **********************************************************************


def update_aws_secret(
    secret_name: str, secret_value: str, region_name: Optional[str] = None
) -> bool:
    # Initialize the AWS Secrets Manager client
    kwargs = {}
    if region_name:
        kwargs["region_name"] = region_name

    secrets_client = boto3.client("secretsmanager", **kwargs)

    # Retrieve the ARN of the secret
    print(f"Updating secret '{secret_name}'")
    secret_description = secrets_client.describe_secret(SecretId=secret_name)
    secret_arn = secret_description["ARN"]

    # Update the secret using the ARN
    _ = secrets_client.update_secret(SecretId=secret_arn, SecretString=secret_value)

    # TODO: Switch to logging
    print(f"Secret '{secret_name}' (ARN: {secret_arn}) updated successfully.")
    return True


# **********************************************************************


def update_secrets(config: Dict[str, Any]) -> Dict[str, bool]:
    # Get all secrets from the configuration
    secrets = get_secrets_from_config(config)

    # Update each secret
    results = {}

    for secret in secrets:
        # Get the secret value from environment variables
        secret_name = secret["secret_name"]
        env_var_name = secret["secret_value_env"]
        secret_value = os.getenv(env_var_name)

        # Update the secret
        outcome = update_aws_secret(secret_name, secret_value, config.get("region"))
        results[secret_name] = outcome

    return results


# **********************************************************************

if __name__ == "__main__":
    aws_config = get_aws_config()
    aws_infra_config = aws_config.get("infrastructure")

    # Update secrets
    results = update_secrets(aws_infra_config)

    # Print results summary
    success_count = sum(1 for success in results.values() if success)
    print(
        f"\nSummary: Updated {success_count} out of {len(results)} secrets successfully."
    )
