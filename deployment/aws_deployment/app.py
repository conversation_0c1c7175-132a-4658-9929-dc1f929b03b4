#!/usr/bin/env python3
from aws_cdk import App

from deployment.aws_deployment.stacks.ExtractionStack import ExtractionStack
from deployment.aws_deployment.stacks.NetworkStack import NetworkStack
from deployment.aws_deployment.stacks.OrchestratorStack import OrchestratorStack
from shared.python.utils.get_aws_config import get_aws_config

app = App()

# ************************************************************************************************

aws_config = get_aws_config()
aws_infra_config = aws_config.get("infrastructure", {})
infra_suffix = aws_infra_config.get("infrastructure-suffix")


# ************************************************************************************************

stacks_config = aws_infra_config.get("stacks")

network_stack = NetworkStack(
    scope=app,
    aws_config=stacks_config.get("network"),
    deployment_env=aws_config.get("deployment-env"),
)

orchestrator_stack = OrchestratorStack(
    scope=app,
    aws_config=stacks_config.get("prefect"),
    deployment_env=aws_config.get("deployment-env"),
    vpc=network_stack.vpc,
    private_hosted_zone=getattr(network_stack, "private_hosted_zone", None),
    region=aws_config.get("deployment-env"),
)

# Get orchestrator IP address using the utility functions
orchestrator_instance = getattr(orchestrator_stack, "ec2_instance", None)
orchestrator_ip = orchestrator_instance.instance_private_ip

extraction_stack = ExtractionStack(
    scope=app,
    aws_config=stacks_config.get("extraction"),
    deployment_env=aws_config.get("deployment-env"),
    vpc=network_stack.vpc,
    security_groups=network_stack.security_groups,
    orchestrator_ip=orchestrator_ip,
)

# Route 53 resources are now created within the respective stacks:
# - Private hosted zone in NetworkStack
# - A records in the stack that owns the corresponding EC2 instance

print("End of app.py")

app.synth()

# ************************************************************************************************
# ************************************************************************************************
