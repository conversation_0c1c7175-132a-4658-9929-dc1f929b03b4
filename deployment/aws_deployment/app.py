#!/usr/bin/env python3
from aws_cdk import App

from deployment.aws_deployment.stacks.ExtractionStack import ExtractionStack
from deployment.aws_deployment.stacks.NetworkStack import NetworkStack
from deployment.aws_deployment.stacks.OrchestratorStack import OrchestratorStack
from deployment.aws_deployment.stacks.Route53Stack import Route53Stack
from shared.python.utils.get_aws_config import get_aws_config

app = App()

# ************************************************************************************************

aws_config = get_aws_config()
aws_infra_config = aws_config.get("infrastructure", {})
infra_suffix = aws_infra_config.get("infrastructure-suffix")


# ************************************************************************************************

stacks_config = aws_infra_config.get("stacks")

network_stack = NetworkStack(
    scope=app,
    aws_config=stacks_config.get("network"),
    deployment_env=aws_config.get("deployment-env"),
)

orchestrator_stack = OrchestratorStack(
    scope=app,
    aws_config=stacks_config.get("prefect"),
    deployment_env=aws_config.get("deployment-env"),
    vpc=network_stack.vpc,
    region=aws_config.get("deployment-env"),
)

# Get orchestrator IP address using the utility functions
orchestrator_instance = getattr(orchestrator_stack, "ec2_instance", None)
orchestrator_ip = orchestrator_instance.instance_private_ip

extraction_stack = ExtractionStack(
    scope=app,
    aws_config=stacks_config.get("extraction"),
    deployment_env=aws_config.get("deployment-env"),
    vpc=network_stack.vpc,
    security_groups=network_stack.security_groups,
    orchestrator_ip=orchestrator_ip,
)

# Create Route53 stack if configuration is provided
route53_config = stacks_config.get("route53")
if route53_config:
    # Collect all EC2 instances from different stacks
    ec2_instances = {}

    # Add bastion instance from network stack
    if hasattr(network_stack, "ec2_instance"):
        bastion_config = stacks_config.get("network", {}).get("ec2", {})
        bastion_id = bastion_config.get("id")
        if bastion_id:
            ec2_instances[bastion_id] = network_stack.ec2_instance

    # Add orchestrator instance from orchestrator stack
    if hasattr(orchestrator_stack, "ec2_instance"):
        orchestrator_config = stacks_config.get("prefect", {}).get("ec2", {})
        orchestrator_id = orchestrator_config.get("id")
        if orchestrator_id:
            ec2_instances[orchestrator_id] = orchestrator_stack.ec2_instance

    route53_stack = Route53Stack(
        scope=app,
        aws_config=route53_config,
        vpc=network_stack.vpc,
        ec2_instances=ec2_instances,
    )

print("End of app.py")

app.synth()

# ************************************************************************************************
# ************************************************************************************************
