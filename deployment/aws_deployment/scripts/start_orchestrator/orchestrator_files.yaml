orchestrator: prefect
files:
  - source_path: "docker/prefect/docker-compose.yml"
    target_path: "/opt/orchestrator/docker-compose.yml"
    executable: false
  - source_path: "docker/prefect/task_runner.sh"
    target_path: "/opt/orchestrator/task_runner.sh"
    executable: true
  - source_path: "shared/bash/functions.sh"
    target_path: "/opt/orchestrator/functions.sh"
    executable: true
  # Python modules to be replaced when pip install ready
  # - source_path: "shared/python/utils/ecs_utils.py"
  #   target_path: "/opt/orchestrator/shared/python/utils/ecs_utils.py"
  #   executable: true
  # - source_path: "shared/python/utils/get_aws_config.py"
  #   target_path: "/opt/orchestrator/shared/python/utils/get_aws_config.py"
  #   executable: true
  # - source_path: "shared/python/utils/process_resources.py"
  #   target_path: "/opt/orchestrator/shared/python/utils/process_resources.py"
  #   executable: true
  # - source_path: "shared/python/utils/get_config_recursive.py"
  #   target_path: "/opt/orchestrator/shared/python/utils/get_config_recursive.py"
  #   executable: true
  # - source_path: "deployment/aws_deployment/scripts/start_orchestrator/prefect_operational_bootstrap/prefect_operational_bootstrap.py"
  #   target_path: "/opt/orchestrator/prefect_operational_bootstrap.py"
  #   executable: true
  # - source_path: "deployment/aws_deployment/scripts/start_orchestrator/prefect_operational_bootstrap/prefect_bootstrap_config.yaml"
  #   target_path: "/opt/orchestrator/prefect_bootstrap_config.yaml"
  #   executable: true
  # - source_path: "deployment/aws_deployment/scripts/start_orchestrator/prefect_operational_bootstrap/pool_job_template.json"
  #   target_path: "/opt/orchestrator/pool_job_template.json"
  #   executable: true
