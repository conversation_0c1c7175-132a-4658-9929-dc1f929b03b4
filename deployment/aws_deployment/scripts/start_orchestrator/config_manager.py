#!/usr/bin/env python3
"""
Configuration management module for orchestrator deployment.
"""

import os
import sys

import yaml

# Add the project root to the Python path to import shared modules
sys.path.append(
    os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../../"))
)


def load_orchestrator_config(config_path: str) -> tuple[dict, str]:
    """
    Load orchestrator configuration from YAML file.

    Args:
        config_path: Path to the configuration file

    Returns:
        Tuple of (config_dict, orchestrator_type)
    """
    try:
        abs_config_path = os.path.abspath(
            os.path.join(os.path.dirname(__file__), "../../../../", config_path)
        )
        with open(abs_config_path, "r") as f:
            copy_config = yaml.safe_load(f)

        orchestrator_type = copy_config.get("orchestrator", "prefect")
        print(f"Using orchestrator type: {orchestrator_type}")
        return copy_config, orchestrator_type
    except Exception as e:
        print(f"Error loading orchestrator config, defaulting to 'prefect': {e}")
        return {}, "prefect"


def validate_environment_variables(orchestrator_type: str) -> dict:
    """
    Validate and retrieve required environment variables.

    Args:
        orchestrator_type: Type of orchestrator

    Returns:
        Dictionary containing validated environment variables
    """
    env_vars = {}

    # Get AWS region
    env_vars["aws_region"] = os.environ.get("AWS_REGION")
    if not env_vars["aws_region"]:
        print("Error: AWS_REGION environment variable not set")
        sys.exit(1)

    # Get orchestrator DB credentials
    db_user_key = f"{orchestrator_type.upper()}_DB_USER"
    env_vars["orchestrator_db_user"] = os.environ.get(db_user_key)
    if not env_vars["orchestrator_db_user"]:
        print(f"Error: {db_user_key} environment variable not set")
        sys.exit(1)

    db_password_key = f"{orchestrator_type.upper()}_DB_PASSWORD"
    env_vars["orchestrator_db_password"] = os.environ.get(db_password_key)
    if not env_vars["orchestrator_db_password"]:
        print(f"Error: {db_password_key} environment variable not set")
        sys.exit(1)

    return env_vars
