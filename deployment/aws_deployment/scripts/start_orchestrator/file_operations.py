#!/usr/bin/env python3
"""
File operations module for orchestrator deployment.
"""

import base64
import sys
from typing import Dict, List


def read_and_encode_file(file_path: str) -> str:
    """
    Read a file and encode its content in base64.

    Args:
        file_path: Path to the file to read

    Returns:
        Base64 encoded content of the file
    """
    try:
        with open(file_path, "rb") as f:
            return base64.b64encode(f.read()).decode("utf-8")
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        sys.exit(1)


def prepare_copy_commands(files_to_copy: List[Dict]) -> List[str]:
    """
    Prepare shell commands to copy files to the remote instance.

    Args:
        files_to_copy: List of file info dictionaries

    Returns:
        List of shell commands
    """
    commands = ["mkdir -p /opt/orchestrator"]

    for file_info in files_to_copy:
        source_path = file_info["source_path"]
        target_path = file_info["target_path"]
        make_executable = file_info.get("executable", False)

        # Read and base64 encode the file content
        file_content = read_and_encode_file(source_path)

        # Create command to copy the file to the instance
        copy_cmd = f"echo {file_content} | base64 -d > {target_path}"
        commands.append(copy_cmd)

        # Make the file executable if specified
        if make_executable:
            commands.append(f"chmod +x {target_path}")

    return commands
