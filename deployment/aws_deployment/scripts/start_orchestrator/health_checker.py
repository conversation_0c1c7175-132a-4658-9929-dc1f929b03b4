#!/usr/bin/env python3
"""
Health checking module for orchestrator deployment.
"""

import time

from ssm_operations import send_ssm_command, wait_for_command_completion


def check_orchestrator_server_health(
    ssm_client, instance_id: str, timeout: int = 480
) -> bool:
    """
    Check if the orchestrator server is running properly by checking container status.

    Args:
        ssm_client: Boto3 SSM client
        instance_id: EC2 instance ID
        timeout: Maximum time to wait in seconds

    Returns:
        True if the orchestrator server is running properly, False otherwise
    """
    start_time = time.time()

    while time.time() - start_time < timeout:
        if _check_container_status(ssm_client, instance_id):
            return True

        print("Waiting for orchestrator server to start...")
        time.sleep(5)

    print(f"Timed out waiting for orchestrator server to start after {timeout} seconds")
    return False


def _check_container_status(ssm_client, instance_id: str) -> bool:
    """
    Check the status of Docker containers.

    Args:
        ssm_client: Boto3 SSM client
        instance_id: EC2 instance ID

    Returns:
        True if containers are running, False otherwise
    """
    try:
        commands = [
            "cd /opt/orchestrator",
            "docker-compose -f docker-compose.yml ps -q | wc -l",
            "docker-compose -f docker-compose.yml ps",
        ]

        command_id = send_ssm_command(
            ssm_client, instance_id, commands, "Check orchestrator server health"
        )

        if wait_for_command_completion(ssm_client, command_id, instance_id, timeout=60):
            return _parse_container_output(ssm_client, command_id, instance_id)

        return False
    except Exception as e:
        print(f"Error checking container status: {e}")
        return False


def _parse_container_output(ssm_client, command_id: str, instance_id: str) -> bool:
    """
    Parse the output of container status check.

    Args:
        ssm_client: Boto3 SSM client
        command_id: Command ID
        instance_id: EC2 instance ID

    Returns:
        True if containers are running, False otherwise
    """
    try:
        response = ssm_client.get_command_invocation(
            CommandId=command_id, InstanceId=instance_id
        )

        output = response.get("StandardOutputContent", "")
        lines = output.strip().split("\n")

        if lines and lines[0].strip().isdigit():
            container_count = int(lines[0].strip())
            if container_count > 0:
                print(
                    f"Orchestrator server is running with {container_count} containers"
                )
                return True

        return False
    except Exception:
        return False
