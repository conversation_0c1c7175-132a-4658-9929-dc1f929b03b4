#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to copy the Orchestrator server files to an EC2 instance and start it.
This script is called from the GitHub workflow after secrets are updated.
It reuses the docker-compose.yml and task_runner.sh files from the Docker Orchestrator directory.
"""

import argparse
import sys

import boto3

# Add the project root to the Python path to import shared modules
# sys.path.append(
#     os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../../"))
# )
from config_manager import (
    load_orchestrator_config,
    validate_environment_variables,
)

from deployment.aws_deployment.scripts.start_orchestrator.file_operations import (
    prepare_copy_commands,
)
from deployment.aws_deployment.scripts.start_orchestrator.health_checker import (
    check_orchestrator_server_health,
)
from deployment.aws_deployment.scripts.start_orchestrator.ssm_operations import (
    prepare_start_commands,
    send_ssm_command,
    wait_for_command_completion,
)
from shared.python.utils.aws_instance_utils import (
    get_instance_id_from_config,
    get_instance_id_from_parameter,
    get_instance_ip_address,
)
from shared.python.utils.get_aws_config import get_aws_account_id


def copy_files_to_instance(ssm_client, instance_id: str, files_to_copy: list) -> str:
    """
    Copy files to an EC2 instance using SSM.

    Args:
        ssm_client: Boto3 SSM client
        instance_id: EC2 instance ID
        files_to_copy: List of file information dictionaries

    Returns:
        The command ID of the SSM command
    """
    commands = prepare_copy_commands(files_to_copy)
    return send_ssm_command(
        ssm_client, instance_id, commands, "Copy files to EC2 instance"
    )


def start_orchestrator_server(
    ssm_client,
    instance_id: str,
    aws_region: str,
    aws_account_id: str,
    orchestrator_db_user: str,
    orchestrator_db_password: str,
    orchestrator_ip: str,
    orchestrator_type: str = "prefect",
) -> str:
    """
    Start the Orchestrator server on an EC2 instance using SSM.

    Args:
        ssm_client: Boto3 SSM client
        instance_id: EC2 instance ID
        aws_region: AWS region
        aws_account_id: AWS account ID
        orchestrator_db_user: Orchestrator database user
        orchestrator_db_password: Orchestrator database password
        orchestrator_ip: IP address for orchestrator
        orchestrator_type: Type of orchestrator

    Returns:
        The command ID of the SSM command
    """
    commands = prepare_start_commands(
        aws_region,
        aws_account_id,
        orchestrator_db_user,
        orchestrator_db_password,
        orchestrator_ip,
        orchestrator_type,
    )

    return send_ssm_command(
        ssm_client,
        instance_id,
        commands,
        "Start orchestrator server after secrets update in detached mode",
    )


def main():
    parser = argparse.ArgumentParser(
        description="Copy and start orchestrator server on EC2"
    )
    parser.add_argument(
        "--config-path",
        default="deployment/aws_deployment/scripts/start_orchestrator/orchestrator_files.yaml",
        help="Path to the orchestrator files configuration YAML",
    )
    args = parser.parse_args()

    # Load configuration
    copy_config, orchestrator_type = load_orchestrator_config(args.config_path)
    env_vars = validate_environment_variables(orchestrator_type)

    # Get AWS account ID
    aws_account_id = get_aws_account_id()
    if not aws_account_id:
        print("Error: Could not retrieve AWS account ID")
        sys.exit(1)

    # Create AWS clients
    ssm_client = boto3.client("ssm", region_name=env_vars["aws_region"])

    # Get EC2 instance information
    parameter_name = get_instance_id_from_config(orchestrator_type)
    instance_id = get_instance_id_from_parameter(ssm_client, parameter_name)
    orchestrator_ip = get_instance_ip_address(instance_id, env_vars["aws_region"])

    # Get files to copy from config
    files_to_copy = copy_config.get("files", [])

    # Copy files to instance
    print(f"Copying orchestrator files to instance {instance_id}...")
    copy_command_id = copy_files_to_instance(ssm_client, instance_id, files_to_copy)

    print("Waiting for files copy to complete...")
    if not wait_for_command_completion(ssm_client, copy_command_id, instance_id):
        print("Failed to copy files to instance")
        sys.exit(1)

    # Start orchestrator server
    print(f"Starting orchestrator server on instance {instance_id}...")
    start_command_id = start_orchestrator_server(
        ssm_client,
        instance_id,
        env_vars["aws_region"],
        aws_account_id,
        env_vars["orchestrator_db_user"],
        env_vars["orchestrator_db_password"],
        orchestrator_ip,
        orchestrator_type,
    )

    print("Waiting for orchestrator server start command to complete...")
    if not wait_for_command_completion(
        ssm_client, start_command_id, instance_id, timeout=480
    ):
        print("Failed to execute orchestrator server start command")
        sys.exit(1)

    # Verify server is running
    print("Verifying orchestrator server is running...")
    if not check_orchestrator_server_health(ssm_client, instance_id, timeout=480):
        print("Failed to verify orchestrator server is running")
        sys.exit(1)

    print(
        f"Orchestrator server successfully started on instance {instance_id} at {orchestrator_ip}"
    )


if __name__ == "__main__":
    main()
