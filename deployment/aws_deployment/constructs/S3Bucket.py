from aws_cdk import aws_s3 as s3, RemovalPolicy, Fn
from constructs import Construct


class S3Bucket(Construct):
    def __init__(self, scope: Construct, id: str, bucket_name: str):
        super().__init__(scope, id)

        # Check if the bucket already exists using CloudFormation condition functions
        # Note: This will work when the bucket exists from a previous deployment where it was retained

        # First, we determine the approach:
        # 1. Try to create a new bucket with the given name
        # 2. If it fails due to the bucket already existing, we'll import it

        self.bucket = s3.Bucket(
            self,
            id,
            bucket_name=bucket_name,
            versioned=True,
            removal_policy=RemovalPolicy.RETAIN,  # RETAIN so bucket persists if stack is deleted
            auto_delete_objects=False,
            # Enable cross region replication rules if needed
            # cross_region_replication_rules=[...],
            # Enable lifecycle rules if needed
            # lifecycle_rules=[...],
        )
