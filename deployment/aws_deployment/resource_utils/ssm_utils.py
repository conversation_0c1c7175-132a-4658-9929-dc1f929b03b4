from typing import Any
from aws_cdk import aws_ssm as ssm


def create_ssm_parameter(scope: Any, config: dict, value: str):
    """
    Create an SSM parameter with the given name and value.

    Args:
        scope: The CDK construct scope to create the parameter in
        config:

    Returns:
        The created SSM parameter
    """
    if config.get("create-parameter-store-variable-with-arn") is True:
        name = config.get("id")

        return ssm.StringParameter(
            scope,
            id=f"{name}-parameter",
            parameter_name=name,
            string_value=value,
            description=f"Parameter: {name}",
        )
