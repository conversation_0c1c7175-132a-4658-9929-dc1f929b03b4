from typing import Dict, List

from aws_cdk import (
    Duration,
)
from aws_cdk import (
    aws_ecr as ecr,
)
from aws_cdk import (
    aws_iam as iam,
)
from aws_cdk import (
    aws_lambda as lambda_,
)
from constructs import Construct

from deployment.aws_deployment.resource_utils.ssm_utils import create_ssm_parameter


def create_lambda_role_from_config(
    scope: Construct,
    role_config: Dict,
) -> iam.Role:
    """
    Create an IAM role for Lambda function from configuration.

    Args:
        scope: The CDK construct scope
        role_config: IAM role configuration
        secrets_arns: List of secret ARNs to grant access to

    Returns:
        The created IAM role
    """
    role_id = role_config.get("id")

    # Create the role with <PERSON><PERSON> as the principal
    role = iam.Role(
        scope,
        role_id,
        role_name=role_id,
        assumed_by=iam.ServicePrincipal("lambda.amazonaws.com"),
        managed_policies=[
            iam.ManagedPolicy.from_aws_managed_policy_name(
                "service-role/AWSLambdaBasicExecutionRole"
            ),
        ],
    )

    # Add the specific permissions required by the prefect operational bootstrap lambda
    lambda_policy = iam.PolicyStatement(
        effect=iam.Effect.ALLOW,
        actions=[
            "ec2:DescribeInstances",
            "ec2:DescribeSubnets",
            "ecs:DescribeTaskDefinition",
            "ssm:GetParameter",
            "ssm:GetParameters",
            "secretsmanager:GetSecretValue",
            "secretsmanager:DescribeSecret",
            "logs:CreateLogGroup",
            "logs:CreateLogStream",
            "logs:PutLogEvents",
        ],
        resources=["*"],
    )
    role.add_to_policy(lambda_policy)

    # Create SSM parameter if specified
    create_ssm_parameter(
        scope,
        config=role_config,
        value=role.role_arn,
    )

    return role


def _parse_ecr_image_uri(image_uri: str) -> tuple[str, str, str, str]:
    """
    Parse ECR image URI to extract repository name, tag, account ID, and region.

    Args:
        image_uri: ECR image URI in format: account.dkr.ecr.region.amazonaws.com/repo:tag

    Returns:
        Tuple of (repository_name, tag, account_id, region)

    Raises:
        ValueError: If the URI format is invalid
    """
    if not image_uri or "/" not in image_uri:
        raise ValueError(f"Invalid image URI format: {image_uri}")

    registry_and_repo = image_uri.split("/", 1)
    if len(registry_and_repo) <= 1:
        raise ValueError(f"Invalid image URI format: {image_uri}")

    repo_and_tag = registry_and_repo[1]

    # Split repository and tag
    if ":" in repo_and_tag:
        repository_name, tag = repo_and_tag.split(":", 1)
    else:
        repository_name = repo_and_tag
        tag = "latest"

    # Get the registry part to extract account and region
    registry_part = registry_and_repo[0]
    registry_components = registry_part.split(".")

    if len(registry_components) >= 4:
        account_id = registry_components[0]
        region = registry_components[3]
    else:
        raise ValueError(f"Invalid ECR registry format in URI: {image_uri}")

    return repository_name, tag, account_id, region


def _create_ecr_repository_reference(
    scope: Construct, lambda_id: str, repository_name: str, account_id: str, region: str
) -> ecr.IRepository:
    """
    Create an ECR repository reference from repository details.

    Args:
        scope: The CDK construct scope
        lambda_id: Lambda function ID for unique naming
        repository_name: Name of the ECR repository
        account_id: AWS account ID
        region: AWS region

    Returns:
        ECR repository reference
    """
    repository_arn = f"arn:aws:ecr:{region}:{account_id}:repository/{repository_name}"

    return ecr.Repository.from_repository_attributes(
        scope,
        f"{lambda_id}-repository",
        repository_name=repository_name,
        repository_arn=repository_arn,
    )


def create_lambda_from_config(
    scope: Construct,
    lambda_config: Dict,
    role: iam.Role,
    deployment_env: str,
    orchestrator_ip: str = None,
    region: str = None,
    # vpc=None,
    # security_group=None,
) -> lambda_.Function:
    """
    Create a Lambda function from configuration using container image.

    Args:
        scope: The CDK construct scope
        lambda_config: Lambda function configuration
        role: IAM role for the Lambda function

    Returns:
        The created Lambda function
    """
    lambda_id = lambda_config.get("id")
    image_uri = lambda_config.get("image")

    if not image_uri:
        raise ValueError("No image URI provided in lambda configuration")

    # Parse the ECR image URI
    repository_name, tag, account_id, region = _parse_ecr_image_uri(image_uri)

    # Create ECR repository reference
    repository = _create_ecr_repository_reference(
        scope, lambda_id, repository_name, account_id, region
    )

    if deployment_env == "prod":
        branch = "main"
    else:
        branch = "dev"

    # Create Lambda function from ECR image
    lambda_function = lambda_.Function(
        scope,
        lambda_id,
        function_name=lambda_id,
        code=lambda_.Code.from_ecr_image(
            repository,
            tag_or_digest=tag,
        ),
        handler=lambda_.Handler.FROM_IMAGE,
        runtime=lambda_.Runtime.FROM_IMAGE,
        role=role,
        timeout=Duration.minutes(15),
        memory_size=512,
        # vpc=vpc,
        # security_groups=[security_group],
        environment={
            "AWS_CONFIG_PATH": "/app/shared/config/aws_config.yaml",
            "BOOTSTRAP_CONFIG_PATH": "/app/src/lambdas/prefect_operational_bootstrap/prefect_bootstrap_config.yaml",
            "PREFECT_API_URL": f"http://{orchestrator_ip}:4200/api",
            "GHUB_BRANCH": branch,
        },
    )

    # Create SSM parameter if specified
    create_ssm_parameter(
        scope,
        config=lambda_config,
        value=lambda_function.function_arn,
    )

    return lambda_function
