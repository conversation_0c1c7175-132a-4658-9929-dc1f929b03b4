from typing import Any, Dict, List

from aws_cdk import aws_ec2 as ec2
from aws_cdk import aws_iam as iam
from constructs import Construct

from deployment.aws_deployment.resource_utils.ssm_utils import create_ssm_parameter


def create_ec2_instance_from_config(
    scope: Construct,
    ec2_config: Dict[str, Any],
    vpc: ec2.Vpc,
    security_groups: List[ec2.SecurityGroup],
    role: iam.Role,
    key_name: str,
    secrets_config: List[Dict[str, Any]] = None,
) -> ec2.Instance:
    """
    Create an EC2 instance from configuration.

    Args:
        scope: The CDK construct scope
        ec2_config: EC2 instance configuration
        vpc: The VPC to place the instance in
        security_groups: Security groups to attach to the instance
        role: IAM role to attach to the instance
        secrets_config: List of secrets to pass as environment variables

    Returns:
        The created EC2 instance
    """
    instance_id = ec2_config.get("id")
    instance_type = ec2_config.get("instance-type", "t2.micro")

    # Create user data to set environment variables and install Docker
    user_data = ec2.UserData.for_linux()

    # Add environment variables for secrets if provided
    if secrets_config:
        env_vars = {}
        for secret in secrets_config:
            secret_id = secret.get("id")
            env_var_name = secret.get("secret-value")
            if secret_id and env_var_name:
                env_vars[env_var_name] = secret_id

        if env_vars:
            user_data.add_commands(
                _generate_env_user_data(
                    env_vars, "/etc/profile.d/orchestrator_secrets.sh"
                )
            )

    # Install Docker, Docker Compose, and other required packages
    user_data.add_commands(
        "yum update -y",
        "yum install -y amazon-linux-extras jq",
        "amazon-linux-extras install docker -y",
        "systemctl start docker",
        "systemctl enable docker",
        "usermod -a -G docker ec2-user",
        'curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose',
        "chmod +x /usr/local/bin/docker-compose",
        "ln -s /usr/local/bin/docker-compose /usr/bin/docker-compose",
        "mkdir -p /opt/orchestrator",
        # Create directory for PostgreSQL data
        "mkdir -p /var/lib/postgresql/data",
        # Format and mount the persistent EBS volume for PostgreSQL data
        "if [ -b /dev/xvdb ]; then",
        "  if ! blkid /dev/xvdb; then",
        "    mkfs -t xfs /dev/xvdb",
        "  fi",
        "  echo '/dev/xvdb /var/lib/postgresql/data xfs defaults 0 2' >> /etc/fstab",
        "  mount -a",
        "  chown 999:999 /var/lib/postgresql/data",  # Set ownership to postgres user (UID 999 in postgres container)
        "fi",
    )

    # Create the EC2 instance
    instance = ec2.Instance(
        scope,
        instance_id,
        instance_type=ec2.InstanceType(instance_type),
        machine_image=ec2.MachineImage.latest_amazon_linux2(),
        vpc=vpc,
        vpc_subnets=ec2.SubnetSelection(
            subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS
        ),  # Use private subnet for better security
        security_group=security_groups[0],  # Primary security group
        role=role,
        user_data=user_data,
        key_pair=ec2.KeyPair.from_key_pair_name(scope, "ImportedKeyPair", key_name),
        block_devices=[
            ec2.BlockDevice(
                device_name="/dev/xvda",
                volume=ec2.BlockDeviceVolume.ebs(
                    volume_size=20,  # 20 GB for Docker images and volumes
                    volume_type=ec2.EbsDeviceVolumeType.GP3,
                ),
            ),
            # Add persistent volume for Prefect database
            ec2.BlockDevice(
                device_name="/dev/xvdb",
                volume=ec2.BlockDeviceVolume.ebs(
                    volume_size=10,  # 10 GB for database
                    volume_type=ec2.EbsDeviceVolumeType.GP3,
                    delete_on_termination=False,  # Make it persistent
                ),
            ),
        ],
    )

    # Add additional security groups if any
    for sg in security_groups[1:]:
        instance.add_security_group(sg)

    # Create SSM parameter if specified
    create_ssm_parameter(
        scope,
        config=ec2_config,
        value=instance.instance_id,
    )

    return instance


def create_ec2_security_group_from_config(
    scope: Construct,
    sg_config: Dict[str, Any],
    vpc: ec2.Vpc,
) -> ec2.SecurityGroup:
    """
    Create a security group for EC2 instance from configuration.

    Args:
        scope: The CDK construct scope
        sg_config: Security group configuration
        vpc: The VPC to create the security group in

    Returns:
        The created security group
    """
    sg_id = sg_config.get("id")

    # Create the security group
    security_group = ec2.SecurityGroup(
        scope,
        sg_id,
        security_group_name=sg_id,
        vpc=vpc,
        description=f"Security group for {sg_id}",
    )

    # Apply egress rules from config
    egress_rules = sg_config.get("egress-rules", [])

    if not egress_rules:
        # Default rule if no rules specified in config
        security_group.add_egress_rule(
            ec2.Peer.any_ipv4(), ec2.Port.tcp(443), "Allow HTTPS outbound"
        )
    else:
        for rule in egress_rules:
            peer = _get_peer_from_config(rule.get("peer", "any-ipv4"))
            port = _get_port_from_config(rule.get("port", {}))
            description = rule.get("description", "Egress rule")

            security_group.add_egress_rule(peer, port, description)

    # Add ingress rule for Orchestrator UI (port 4200)
    security_group.add_ingress_rule(
        ec2.Peer.any_ipv4(), ec2.Port.tcp(4200), "Allow access to Orchestrator UI"
    )

    # We'll add the SSH ingress rule from the bastion host in the OrchestratorStack
    # This is done there to ensure we have access to the bastion security group

    # Create SSM parameter if specified
    create_ssm_parameter(
        scope,
        config=sg_config,
        value=security_group.security_group_id,
    )

    return security_group


def create_ec2_role_from_config(
    scope: Construct,
    role_config: Dict[str, Any],
    secrets_arns: List[str] = None,
) -> iam.Role:
    """
    Create an IAM role for EC2 instance from configuration.

    Args:
        scope: The CDK construct scope
        role_config: IAM role configuration
        secrets_arns: List of secret ARNs to grant access to

    Returns:
        The created IAM role
    """
    role_id = role_config.get("id")

    # Create the role with EC2 as the principal
    role = iam.Role(
        scope,
        role_id,
        role_name=role_id,
        assumed_by=iam.ServicePrincipal("ec2.amazonaws.com"),
        managed_policies=[
            iam.ManagedPolicy.from_aws_managed_policy_name(
                "AmazonSSMManagedInstanceCore"
            ),
            # Add ECR read access for pulling Docker images
            iam.ManagedPolicy.from_aws_managed_policy_name(
                "AmazonEC2ContainerRegistryReadOnly"
            ),
        ],
    )

    # Add permissions to access secrets if provided
    if secrets_arns:
        secrets_policy = iam.PolicyStatement(
            actions=["secretsmanager:GetSecretValue", "secretsmanager:DescribeSecret"],
            resources=secrets_arns,
        )
        role.add_to_policy(secrets_policy)

    # Add permissions to describe EC2 instances (for getting metadata)
    role.add_to_policy(
        iam.PolicyStatement(
            actions=["ec2:DescribeInstances"],
            resources=["*"],
        )
    )

    # Create SSM parameter if specified
    create_ssm_parameter(
        scope,
        config=role_config,
        value=role.role_arn,
    )

    return role


def _get_peer_from_config(peer_config: str) -> ec2.IPeer:
    """Convert peer config string to ec2.Peer object"""
    if peer_config == "any-ipv4":
        return ec2.Peer.any_ipv4()
    elif peer_config == "any-ipv6":
        return ec2.Peer.any_ipv6()
    elif peer_config.startswith("cidr:"):
        return ec2.Peer.ipv4(peer_config[5:])
    # Add other peer types as needed
    return ec2.Peer.any_ipv4()  # Default


def _get_port_from_config(port_config: Dict[str, Any]) -> ec2.Port:
    """Convert port config to ec2.Port object"""
    port_type = port_config.get("type", "tcp")
    port_number = port_config.get("number", 443)

    if port_type == "tcp":
        return ec2.Port.tcp(port_number)
    elif port_type == "udp":
        return ec2.Port.udp(port_number)
    elif port_type == "all":
        return ec2.Port.all_traffic()
    # Add other port types as needed
    return ec2.Port.tcp(port_number)  # Default


def _generate_env_user_data(env_vars: Dict[str, str], profile_path: str) -> str:
    """
    Generate user data script to set environment variables.

    Args:
        env_vars: Dictionary of environment variables to set
        profile_path: Path to write the environment variables to

    Returns:
        User data script
    """
    env_exports = "\n".join(
        [f'export {key}="{value}"' for key, value in env_vars.items()]
    )

    return f"""
# Create environment variables file
cat > {profile_path} << 'EOL'
{env_exports}
EOL

# Make it executable
chmod +x {profile_path}

# Create directory for Orchestrator
mkdir -p /opt/orchestrator

"""
