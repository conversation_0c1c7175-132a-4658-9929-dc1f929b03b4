import boto3
from aws_cdk import aws_s3 as s3
from aws_cdk import aws_secretsmanager as secretsmanager
from constructs import Construct


def resource_namify(*args):
    return "-".join(map(str, args))


def create_buckets_from_config(scope: Construct, bucket_config):
    created_buckets = {}
    s3_client = boto3.client("s3")

    for bucket_info in bucket_config:
        # Extract values from config
        bucket_id = bucket_info.get("id")

        # Check if the bucket already exists
        try:
            s3_client.head_bucket(Bucket=bucket_id)
            # Bucket exists, import it
            bucket = s3.Bucket.from_bucket_name(
                scope, f"{bucket_id}-existing", bucket_id
            )
            print(f"Imported existing bucket: {bucket_id}")
        except Exception:
            # Bucket doesn't exist, create a new one
            bucket = s3.Bucket(scope, bucket_id, bucket_name=bucket_id)
            print(f"Created new bucket: {bucket_id}")

        # Store the created or imported bucket in our dictionary
        created_buckets[bucket_id] = bucket

    return created_buckets


def create_secrets_from_config(scope: Construct, secrets_config):
    created_secrets = {}

    for secret_config in secrets_config:
        # Extract values from config
        secret_id = secret_config.get("id")

        # Create the secret
        secret = secretsmanager.Secret(scope, secret_id, secret_name=secret_id)

        # Store the created secret in our dictionary
        created_secrets[secret_id] = secret

    return created_secrets
