from aws_cdk import aws_ec2 as ec2
from constructs import Construct

from deployment.aws_deployment.resource_utils.ssm_utils import create_ssm_parameter


def create_vpc_from_config(scope: Construct, vpc_config):
    vpc_id = vpc_config.get("id")

    # Create the VPC
    vpc = ec2.Vpc(
        scope,
        vpc_id,
        vpc_name=vpc_id,
        max_azs=2,
        subnet_configuration=[
            ec2.SubnetConfiguration(
                name="public", subnet_type=ec2.SubnetType.PUBLIC, cidr_mask=24
            ),
            ec2.SubnetConfiguration(
                name="private",
                subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS,
                cidr_mask=24,
            ),
        ],
        nat_gateways=1,  # No NAT gateways as per requirements
    )

    create_ssm_parameter(
        scope,
        config=vpc_config,
        value=vpc.vpc_id,
    )

    return vpc


def _create_security_group(scope: Construct, sg_info: str, vpc: ec2.Vpc):
    """Helper function to create a single security group."""
    sg_id = sg_info.get("id")
    security_group = ec2.SecurityGroup(
        scope,
        sg_id,
        security_group_name=sg_id,
        vpc=vpc,
        description=f"Security group for {sg_id}",
    )

    # Apply egress rules from config
    _apply_security_group_rules(security_group, sg_info)

    create_ssm_parameter(
        scope,
        config=sg_info,
        value=security_group.security_group_id,
    )

    return security_group


def _apply_security_group_rules(security_group: ec2.SecurityGroup, sg_info: dict):
    """Apply security group rules from config"""
    # Process egress rules if defined in config
    egress_rules = sg_info.get("egress-rules", [])

    if not egress_rules:
        # Default rule if no rules specified in config
        security_group.add_egress_rule(
            ec2.Peer.any_ipv4(), ec2.Port.tcp(443), "Allow HTTPS outbound"
        )
        return

    for rule in egress_rules:
        peer = _get_peer_from_config(rule.get("peer", "any-ipv4"))
        port = _get_port_from_config(rule.get("port", {}))
        description = rule.get("description", "Egress rule")

        security_group.add_egress_rule(peer, port, description)


def _get_peer_from_config(peer_config):
    """Convert peer config string to ec2.Peer object"""
    if peer_config == "any-ipv4":
        return ec2.Peer.any_ipv4()
    elif peer_config == "any-ipv6":
        return ec2.Peer.any_ipv6()
    elif peer_config.startswith("cidr:"):
        return ec2.Peer.ipv4(peer_config[5:])
    # Add other peer types as needed
    return ec2.Peer.any_ipv4()  # Default


def _get_port_from_config(port_config):
    """Convert port config to ec2.Port object"""
    port_type = port_config.get("type", "tcp")
    port_number = port_config.get("number", 443)

    if port_type == "tcp":
        return ec2.Port.tcp(port_number)
    elif port_type == "udp":
        return ec2.Port.udp(port_number)
    elif port_type == "all":
        return ec2.Port.all_traffic()
    # Add other port types as needed
    return ec2.Port.tcp(port_number)  # Default


def create_security_groups_from_config(scope: Construct, security_group_config, vpc):
    created_security_groups = {}

    for sg_info in security_group_config:
        sg_id = sg_info.get("id")
        created_security_groups[sg_id] = _create_security_group(scope, sg_info, vpc)

    return created_security_groups


def _create_gateway_endpoint(vpc: ec2.Vpc, endpoint_id: str):
    """Helper function to create a gateway VPC endpoint."""
    service = ec2.GatewayVpcEndpointAwsService.S3
    return vpc.add_gateway_endpoint(
        endpoint_id,
        service=service,
        # Use private subnets instead of public for gateway endpoints
        subnets=[{"subnetType": ec2.SubnetType.PRIVATE_WITH_EGRESS}],
    )


def _create_interface_endpoint(
    vpc: ec2.Vpc,
    endpoint_id: str,
    service: ec2.InterfaceVpcEndpointAwsService,
    security_groups: list,
):
    """Helper function to create an interface VPC endpoint."""
    return vpc.add_interface_endpoint(
        endpoint_id,
        service=service,
        # Explicitly specify we want to use private subnets
        subnets=ec2.SubnetSelection(subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS),
        security_groups=security_groups,
        private_dns_enabled=True,
    )


def _get_interface_service(endpoint_id: str):
    """Helper function to determine the service for an interface VPC endpoint."""
    if "ecr-api" in endpoint_id:
        return ec2.InterfaceVpcEndpointAwsService.ECR
    elif "ecr-dkr" in endpoint_id:
        return ec2.InterfaceVpcEndpointAwsService.ECR_DOCKER
    elif "logs" in endpoint_id:
        return ec2.InterfaceVpcEndpointAwsService.CLOUDWATCH_LOGS
    # Add more services as needed
    return None


def create_vpc_endpoints_from_config(
    scope: Construct, vpc_endpoint_config, vpc, security_groups
):
    created_endpoints = {}

    for endpoint_info in vpc_endpoint_config:
        endpoint_id = endpoint_info.get("id")

        # Determine if it's a gateway or interface endpoint
        if "s3" in endpoint_id:
            endpoint = _create_gateway_endpoint(vpc, endpoint_id)
        else:
            service = _get_interface_service(endpoint_id)
            if service:
                endpoint = _create_interface_endpoint(
                    vpc, endpoint_id, service, list(security_groups.values())
                )
            else:
                # Skip if we don't recognize the service
                continue

        created_endpoints[endpoint_id] = endpoint

    return created_endpoints
