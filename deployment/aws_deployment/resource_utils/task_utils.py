from typing import Any, Dict, List

from aws_cdk import (
    aws_ec2 as ec2,
)
from aws_cdk import (
    aws_ecs as ecs,
)
from aws_cdk import (
    aws_iam as iam,
)

from deployment.aws_deployment.resource_utils.ssm_utils import create_ssm_parameter


def create_ecs_cluster(
    scope,
    vpc: ec2.Vpc,
    cluster_config: str = None,
) -> ecs.Cluster:
    """
    Create an ECS cluster.

    Args:
        scope: The CDK construct scope
        vpc: The VPC to place the cluster in
        cluster_id: The ID to use for the ECS cluster

    Returns:
        The created ECS cluster
    """
    cluster = ecs.Cluster(scope, cluster_config.get("id"), vpc=vpc)

    create_ssm_parameter(
        scope,
        config=cluster_config,
        value=cluster.cluster_arn,
    )
    return cluster


def create_ecs_tasks_from_config(
    scope,
    task_config: List[Dict[str, Any]],
    iam_roles: Dict[str, iam.Role] = None,
    orchestrator_ip: str = None,
) -> Dict[str, ecs.FargateTaskDefinition]:
    """
    Create ECS Fargate task definitions from configuration.

    Args:
        scope: The CDK construct scope
        task_config: List of task configuration dictionaries
        iam_roles: Dictionary of IAM roles to use for tasks
        orchestrator_ip: IP address of the orchestrator for PREFECT_API_URL

    Returns:
        Dictionary of task definitions by ID
    """
    if not task_config:
        return {}

    task_definitions = {}
    iam_roles = iam_roles or {}

    for task in task_config:
        task_id = task.get("id")
        parameters = task.get("parameters", {})

        # Get role ARNs if specified
        execution_role = iam_roles.get("task-execution", {})
        task_role = iam_roles.get("task", {})

        # Create task definition with roles if provided
        task_definition = ecs.FargateTaskDefinition(
            scope,
            task_id,
            memory_limit_mib=int(parameters.get("memory", "512")),
            cpu=int(parameters.get("cpu", "256")),
            execution_role=execution_role,
            task_role=task_role,
        )

        # Add container definition
        container_definitions = parameters.get("container-definitions", {})
        container = task_definition.add_container(
            f"{task_id}-container",
            image=ecs.ContainerImage.from_registry(
                container_definitions.get("image", "")
            ),
            logging=ecs.LogDrivers.aws_logs(stream_prefix=task_id),
        )

        # Add environment variables if specified
        if "environment" in container_definitions:
            for env_var in container_definitions.get("environment", []):
                container.add_environment(
                    name=env_var.get("name", ""), value=env_var.get("value", "")
                )

        # Add PREFECT_API_URL environment variable if orchestrator IP is provided
        if orchestrator_ip:
            container.add_environment(
                name="PREFECT_API_URL", value=f"http://{orchestrator_ip}:4200/api"
            )

        # Store the task definition
        task_definitions[task_id] = task_definition

        create_ssm_parameter(
            scope,
            config=task,
            value=task_definition.task_definition_arn,
        )

    return task_definitions
