version: "2"
networks:
    # This special network is configured so that the local metadata
    # service can bind to the specific IP address that ECS uses
    # in production
    credentials_network:
        driver: bridge
        ipam:
            config:
                - subnet: "*************/24"
                  gateway: *************
services:
    # This container vends credentials to your containers
    ecs-local-endpoints:
        # The Amazon ECS Local Container Endpoints Docker Image
        image: amazon/amazon-ecs-local-container-endpoints
        volumes:
          # Mount /var/run so we can access docker.sock and talk to <PERSON><PERSON>
          - /var/run:/var/run
          # Mount the shared configuration directory, used by the AWS CLI and AWS SDKs
          # On Windows, this directory can be found at "%UserProfile%\.aws"
          # Make sure this is the devcontainer's host directory
          - $HOST_HOME/.aws/:/home/<USER>/
          # - /home/<USER>/.aws/:/home/<USER>/
        environment:
          # define the home folder; credentials will be read from $HOME/.aws
          HOME: "/home"
          # You can change which AWS CLI Profile is used
          AWS_PROFILE: "default"
        networks:
            credentials_network:
                # This special IP address is recognized by the AWS SDKs and AWS CLI 
                ipv4_address: "*************"
                
    # Here we reference the application container that we are testing
    # You can test multiple containers at a time, simply duplicate this section
    # and customize it for each container, and give it a unique IP in 'credentials_network'.
    app:
        depends_on:
            - ecs-local-endpoints
        networks:
            credentials_network:
                ipv4_address: "*************"
        environment:
          AWS_DEFAULT_REGION: "us-east-2"
          # AWS_CONTAINER_CREDENTIALS_RELATIVE_URI: "/creds"
          AWS_CONTAINER_CREDENTIALS_RELATIVE_URI: "/role/extraction-iam-ecs-task-role-dev-us-east-2"
          # AWS_CONTAINER_CREDENTIALS_RELATIVE_URI: "/role/extraction-iam-ecs-task-execution-role-dev-us-east-2"