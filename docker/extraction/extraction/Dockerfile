# Start with Ubuntu 24.04 and Python 3.12.1
FROM python:3.12.1-slim

# Set working directory
WORKDIR /app

# Copy requirements.txt from the same folder as the Dockerfile
COPY docker/extraction/extraction/requirements.txt .

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the extraction code and shared code, preserving folder structure
COPY src/tasks/extraction/ /app/src/tasks/extraction/
COPY shared/ /app/shared/

# Set environment variables
ENV AWS_CONFIG_PATH=/app/shared/config/aws_config.yaml
ENV AWS_REGION=us-east-2
ENV ENDPOINTS=teams
ENV PYTHONPATH=/app

# Set the entry point to extraction.py
CMD ["python", "src/tasks/extraction/extraction.py"]
# CMD ["tail", "-f", "/dev/null"]

