FROM prefecthq/prefect:3.3.3-python3.11

# Install additional packages if needed
RUN pip install prefect-aws

# Install curl
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /opt/prefect

# Set entrypoint
ENTRYPOINT ["/opt/prefect/entrypoint.sh", "prefect", "server", "start"]

# Default environment variables
ENV PREFECT_SERVER_API_HOST=0.0.0.0