FROM public.ecr.aws/lambda/python:3.12

# Set working directory
WORKDIR /app

# Copy requirements.txt from the same folder as the Dockerfile
COPY docker/prefect/operational_bootstrap/requirements.txt .

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the code and shared code, preserving folder structure
COPY src/lambdas/prefect_operational_bootstrap/ /app/src/lambdas/prefect_operational_bootstrap/
COPY shared/ /app/shared/

# Set environment variables
ENV AWS_CONFIG_PATH=/app/shared/config/aws_config.yaml
ENV AWS_REGION=us-east-2
ENV PYTHONPATH=/app

CMD ["src.lambdas.prefect_operational_bootstrap.prefect_operational_bootstrap.lambda_handler"]