services:
  # Prefect Database
  database:
    image: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/prefect:database-latest
    restart: always
    expose:
      - 5432
    volumes:
      - db:/var/lib/postgresql/data # This must match the EBS volume mount point in the user data script
    environment:
      - POSTGRES_USER=${PREFECT_DB_USER}
      - POSTGRES_PASSWORD=${PREFECT_DB_PASSWORD}
      - POSTGRES_DB=prefect
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${PREFECT_DB_USER} -d prefect"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    profiles: ["server"]

  # Prefect Server API and UI
  server:
    image: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/prefect:server-latest
    restart: always
    volumes:
      - prefect:/root/.prefect
    environment:
      - PREFECT_UI_URL=http://${PREFECT_IP}:${PREFECT_SITE_API_PORT}/api
      # The url that the web app from browser should call.
      - PREFECT_API_URL=http://${PREFECT_IP}:${PREFECT_SITE_API_PORT}/api
      - PREFECT_API_DATABASE_CONNECTION_URL=postgresql+asyncpg://${PREFECT_DB_USER}:${PREFECT_DB_PASSWORD}@database:5432/prefect
    ports:
      - 4200:4200
    depends_on:
      database:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:4200/health || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 15s
    profiles: ["server"]

  # Prefect Workers
  worker:
    image: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/prefect:worker-latest
    restart: always
    command: ["--pool", "ecs-pool"]  # Override the default pool
    environment:
      - PREFECT_API_URL=http://server:${PREFECT_PORT}/api
      # For local testing. Not used in prod
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_DEFAULT_REGION=${AWS_REGION}
    depends_on:
      cli:
        condition: service_completed_successfully
    profiles: ["worker"]
    
      # Prefect CLI to create work pool if it doesn't exist
  cli:
    image: prefecthq/prefect:3.3.3-python3.11
    entrypoint: ["/bin/bash", "-c"]
    command:
      - |
        echo 'Prefect server is ready, proceeding with work pool setup...'

        echo 'Checking if work pool ecs-pool exists...'
        if ! prefect work-pool ls | grep 'ecs-pool'; then
          echo 'Creating work pool ecs-pool...'
          prefect work-pool create --type ecs ecs-pool
          echo 'Work pool ecs-pool created successfully'
        else
          echo 'Work pool ecs-pool already exists'
        fi
    environment:
      - PREFECT_API_URL=http://server:${PREFECT_PORT}/api
    depends_on:
      server:
        condition: service_healthy
    profiles: ["worker"]


volumes:
  prefect:
  db:
networks:
  default:
    name: prefect-network
