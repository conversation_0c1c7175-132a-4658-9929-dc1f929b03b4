from aws_cdk import (
    Stack,
)
from aws_cdk import (
    aws_ec2 as ec2,
)
from aws_cdk import (
    aws_ecs as ecs,
)
from aws_cdk import (
    aws_iam as iam,
)
from aws_cdk import (
    aws_logs as logs,
)
from constructs import Construct


class FargateTaskStack(Stack):
    def __init__(self, scope: Construct, construct_id: str, vpc, **kwargs) -> None:
        super().__init__(scope, construct_id, **kwargs)

        # Create the ECS cluster using the VPC from the network stack
        cluster = ecs.Cluster(self, "TaskCluster", vpc=vpc)

        # Create a Fargate task definition
        task_definition = ecs.FargateTaskDefinition(
            self,
            "TaskDef",
            cpu=256,  # 0.25 vCPU - minimum for Fargate
            memory_limit_mib=512,  # 0.5 GB - minimum for Fargate
        )

        # Add a container to the task definition
        container = task_definition.add_container(
            "AppContainer",
            image=ecs.ContainerImage.from_registry("amazon/amazon-ecs-sample"),
            logging=ecs.LogDrivers.aws_logs(
                stream_prefix="fargate-app", log_retention=logs.RetentionDays.ONE_WEEK
            ),
            # Environment variables if needed
            environment={"ENVIRONMENT": "dev"},
            # Container health check
            health_check=ecs.HealthCheck(
                command=["CMD-SHELL", "curl -f http://localhost/ || exit 1"],
                interval=Duration.seconds(30),
                timeout=Duration.seconds(5),
                retries=3,
            ),
        )

        # Add port mappings to the container
        container.add_port_mappings(ecs.PortMapping(container_port=80))

        # Create the Fargate service
        fargate_service = ecs.FargateService(
            self,
            "FargateService",
            cluster=cluster,
            task_definition=task_definition,
            desired_count=1,
            # Ensure the service runs in private subnets
            vpc_subnets=ec2.SubnetSelection(
                subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS
            ),
            assign_public_ip=False,
            # Optional: security group configuration
            security_groups=[
                ec2.SecurityGroup(
                    self,
                    "ServiceSecurityGroup",
                    vpc=vpc,
                    description="Security group for Fargate service",
                    allow_all_outbound=True,
                )
            ],
        )


from aws_cdk import App
from fargate_task_stack import FargateTaskStack
from network_stack import NetworkStack

app = App()
network_stack = NetworkStack(app, "NetworkStack")
fargate_stack = FargateTaskStack(app, "FargateTaskStack", vpc=network_stack.vpc)

# Add dependency to ensure network stack is created first
fargate_stack.add_dependency(network_stack)

app.synth()
