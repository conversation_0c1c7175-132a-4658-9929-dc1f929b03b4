import json
import os

from markdown_it import MarkdownIt
from markdown_it.token import Token
from markdown_it.tree import SyntaxTreeN<PERSON>
from selectolax.parser import <PERSON><PERSON><PERSON><PERSON><PERSON>


def markdown_table_to_json(markdown_str, output_file=None):
    """
    Parse a markdown table and convert it to JSON.

    Args:
        markdown_str (str): The markdown content containing a table
        output_file (str, optional): Path to save the JSON output. Defaults to None.

    Returns:
        list: A list of dictionaries representing the table rows
    """
    table_data = parse_markdown_table(markdown_str)

    # Save to file if output_file is provided
    if output_file and table_data:
        try:
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, "w") as f:
                json.dump(table_data, f, indent=2)
            print(f"Table data saved as JSON to {output_file}")
        except Exception as e:
            print(f"Error saving JSON to file: {e}")

    return table_data


def parse_markdown_table(markdown: str) -> list[dict]:
    md = MarkdownIt("commonmark").enable("table")
    tokens = md.parse(markdown)

    headers = []
    rows = []

    in_header = False
    in_row = False
    current_row = []

    for i, token in enumerate(tokens):
        if token.type == "thead_open":
            in_header = True
        elif token.type == "thead_close":
            in_header = False
        elif token.type == "tbody_open":
            in_row = True
        elif token.type == "tbody_close":
            in_row = False

        elif token.type == "tr_open":
            current_row = []

        elif token.type == "tr_close":
            if in_header:
                headers = current_row
            elif in_row:
                row_dict = {headers[i]: cell for i, cell in enumerate(current_row)}
                rows.append(row_dict)

        elif token.type in {"th_open", "td_open"}:
            # Get the next inline token with actual content
            inline_token = tokens[i + 1]
            content = ""
            if inline_token.type == "inline":
                content = inline_token.content.strip()
            current_row.append(content)

    return rows


def html_table_to_json(html: str) -> str:
    tree = HTMLParser(html)
    table = tree.css_first("table")
    if not table:
        return json.dumps([])  # No table found

    rows = table.css("tr")
    if not rows:
        return json.dumps([])  # Empty table

    # Extract header cells (th or td)
    headers = [cell.text(strip=True) for cell in rows[0].css("th, td")]

    data = []
    for row in rows[1:]:
        cells = [cell.text(strip=True) for cell in row.css("td, th")]
        if cells:
            row_dict = {
                headers[i]: cells[i] for i in range(min(len(headers), len(cells)))
            }
            data.append(row_dict)

    return json.dumps(data, indent=2)


def markdown_html_parser(response_data):
    # Extract markdown content if available
    if "data" in response_data and "markdown" in response_data["data"]:
        markdown_content = response_data["data"]["markdown"]

        # Convert markdown table to JSON
        json_output_path = "src/tasks/extraction/table_data.json"
        table_data = markdown_table_to_json(markdown_content, json_output_path)

        if table_data:
            print(f"Successfully extracted {len(table_data)} rows from the table")
        else:
            print("No table data found in the markdown content")
    elif "data" in response_data and "html" in response_data["data"]:
        html_content = response_data["data"]["html"]

        # Convert HTML table to JSON
        json_output_path = "src/tasks/extraction/table_data_html.json"
        json_data = html_table_to_json(html_content)

        # Save JSON to file
        try:
            os.makedirs(os.path.dirname(json_output_path), exist_ok=True)
            with open(json_output_path, "w") as f:
                f.write(json_data)
            print(f"HTML table data saved as JSON to {json_output_path}")

            # Parse the saved JSON to get row count for the log message
            table_data = json.loads(json_data)
            if table_data:
                print(
                    f"Successfully extracted {len(table_data)} rows from the HTML table"
                )
            else:
                print("No table data found in the HTML content")
        except Exception as e:
            print(f"Error saving HTML table JSON to file: {e}")
