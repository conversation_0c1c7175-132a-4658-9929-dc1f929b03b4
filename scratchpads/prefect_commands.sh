prefect config set PREFECT_API_URL=http://<your-wsl-ip>:4200/api
prefect config set PREFECT_API_URL=http://0.0.0.0:4200/api
prefect config set PREFECT_API_URL=http://localhost:4200/api
prefect config set PREFECT_API_URL=http://host.docker.internal:4200/api
prefect config set PREFECT_API_URL=http://server:4200/api
netsh interface portproxy add v4tov4 listenport=4200 listenaddress=0.0.0.0 connectport=4200 connectaddress=$(wsl hostname -I)
New-NetFirewallRule -DisplayName "Prefect UI" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 4200
